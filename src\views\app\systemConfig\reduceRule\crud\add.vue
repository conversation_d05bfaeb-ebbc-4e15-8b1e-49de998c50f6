<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form :config="basicFormConfig" pageType="add" @onSubmit="onBasicSubmit"/>
  </div>
</template>

<script setup>
import {showToast} from 'vant'
import {useRoute} from 'vue-router'

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    // 如果有id参数，则为编辑模式
    ...(route.query.id ? {id: route.query.id} : {})
  },
  curl: {
    add: '/discount_rule/post_add', // 新增接口
    edit: '/discount_rule/post_modify', // 编辑接口
    info: '/discount_rule/get_info' // 获取详情接口（编辑时需要）
  },
  groupForm: [
    [0, 1],
    [1, 2],
    [2, 3],
    [3, 4],
    [4, 5],
  ],
  form: [
    {
      label: "规则名称",
      key: "rule_name",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入",
      required: true,
      rules: [{required: true, message: "请填写减免规则名称"}],
    },
    {
      label: "减免方式",
      key: "discount_type",
      component: "yhc-picker",
      // default: 1,
      opts: {
        url: "",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: "id",
        defaultList: [
          {id: "0", title: "固定金额"},
          {id: "1", title: "订单百分比"},
        ],
      },
      child: {
        showMode: true,
        map: {
          "0": [
            {
              label: "补贴金额",
              key: "discount_amount",
              component: "yhc-input",
              type: "number",
              placeholder: "请输入",
              "right-icon": "元",
              min: 0.01,
              decimalPlaces: true
            },
          ],
          "1": [
            {
              label: "百分比",
              key: "discount_percentage",
              component: "yhc-input",
              type: "number",
              placeholder: "请输入",
              "right-icon": "%",
              min: 1,
              max: 100
            },
          ],
        },
        form: [],
      },
    },
    {
      label: "满额减免",
      key: "min_amount_enabled",
      component: "yhc-radio-group",
      default: "0",
      options: [
        {value: 0, label: "无减免"},
        {value: 1, label: "减免"}
      ],
      shape: "dot",
      // required: true,
      child: {
        map: {
          "1": [
            {
              label: "账单金额",
              key: "min_amount",
              component: "yhc-input",
              type: "number",
              placeholder: "请输入",
              "right-icon": "元",
              // min:0.01,
              decimalPlaces: true
            },
            {

              label: "填100，账单金额满100元享受减免",
              key: "desc",
              component: "yhc-desc",
            },
          ],
        },
        form: [],
      },
    },
    {
      label: "减免上限",
      key: "limit_type",
      component: "yhc-radio-group",
      default: "0",
      options: [
        {value: 0, label: "无上限"},
        {value: 1, label: "上限"}
      ],
      shape: "dot",
      // required: true,
      child: {
        map: {
          "1": [
            {
              label: "上限金额",
              key: "limit_amount",
              component: "yhc-input",
              type: "number",
              placeholder: "请输入",
              "right-icon": "元",
              // min:0.01,
              decimalPlaces: true
            },
            {
              label: "统计周期",
              key: "selectAppearance",
              default: 0,
              component: "yhc-picker",
              placeholder: "请选择",
              opts: {
                url: "",
                postData: {},
                merge: false,
                multiple: false,
                text_key: "title",
                contrast_key: "id",
                keyMap: "id",
                defaultList: [
                  {id: "0", title: "每餐"},
                  {id: "1", title: "每天"},
                  {id: "2", title: "每周"},
                  {id: "3", title: "每月"},
                ]
              },
            },
          ],
        },
        form: [],
      },
    },
    {
      label: "考勤规则",
      key: "attendance_rule_id",
      component: "yhc-picker",
      placeholder: "请选择",
      popup: {},
      opts: {
        url: "/attendance_rule/get_all",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "rule_name",
        contrast_key: "id",
        keyMap: "id",
        defaultList: [],
      },
    }

  ]
}
const {proxy} = getCurrentInstance();
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改规则' : '新增规则',
  });
};
setRightA()
// // 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
