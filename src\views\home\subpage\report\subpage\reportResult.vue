<template>
  <div class="payment-success-page">
    <!-- 成功状态卡片 -->
    <div class="success-card">
      <!-- 成功图标 -->
      <div class="success-icon">
        <img src="../../../../../static/img/失败&警告.svg" alt="">
      </div>

      <!-- 付款成功文字 -->
      <div class="success-text">付款成功</div>

      <!-- 金额显示 -->
      <div class="amount-text">{{ formatAmount(paymentInfo.amount) }}</div>

      <!-- 详细信息 -->
      <div class="detail-info">
        <div class="info-row">
          <span class="info-label">餐厅</span>
          <span class="info-value">{{ paymentInfo.restaurant || 'A001' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">支付方式</span>
          <span class="info-value">{{ paymentInfo.paymentMethod || '余额支付' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">支付时间</span>
          <span class="info-value">{{ paymentInfo.paymentTime || formatCurrentTime() }}</span>
        </div>
      </div>
    </div>
    <div class="bottom">
      <van-button type="primary" @click="router.go(-2)" style="width: 112px;
height: 44px;border-radius: 8px;font-size: 17px;">返回首页</van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import dayjs from 'dayjs'

// 路由相关
const router = useRouter()
const route = useRoute()

// 支付信息数据
const paymentInfo = ref({
  amount: 0,
  restaurant: '',
  paymentMethod: '',
  paymentTime: ''
})

/**
 * 格式化金额显示
 * @param {number} amount - 金额
 * @returns {string} 格式化后的金额字符串
 */
const formatAmount = (amount) => {
  if (!amount) return '-68.08'
  return amount > 0 ? `+${amount.toFixed(2)}` : `${amount.toFixed(2)}`
}

/**
 * 格式化当前时间
 * @returns {string} 格式化后的时间字符串
 */
const formatCurrentTime = () => {
  return dayjs().format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 返回首页
 */
const goHome = () => {
  router.push({
    path: '/home'
  })
}

/**
 * 初始化页面数据
 */
const initPageData = () => {
  // 从路由参数获取支付信息
  const queryData = route.query
  if (queryData) {
    paymentInfo.value = {
      amount: queryData.amount ? parseFloat(queryData.amount) : -68.08,
      restaurant: queryData.restaurant || 'A001',
      paymentMethod: queryData.paymentMethod || '余额支付',
      paymentTime: queryData.paymentTime || formatCurrentTime()
    }
  } else {
    // 默认数据（与设计图保持一致）
    paymentInfo.value = {
      amount: -68.08,
      restaurant: 'A001',
      paymentMethod: '余额支付',
      paymentTime: '2024-04-10 14:08'
    }
  }
}

// 页面挂载时初始化数据
onMounted(() => {
  initPageData()
})
</script>

<style lang="scss" scoped>
.payment-success-page {

  .success-card {
    background: #ffffff;
    // border-radius: 8px;
    // padding: 40px 16px 32px;
    // margin-bottom: 24px;
    text-align: center;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .success-icon {
      padding-top: 16px;
    }

    .success-text {
      font-size: 16px;
      color: #1989fa;
      padding-top: 10px;
      margin-bottom: 16px;
      font-weight: 500;
    }

    .amount-text {
      font-size: 32px;
      font-weight: bold;
      color: #323233;
      margin-bottom: 32px;
      line-height: 1.2;
    }

    .detail-info {
      padding: 16px 26px;

      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 16px;
        // padding: 16px 0px;
        // margin-bottom: 8px;
        font-size: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          color: #646566;
        }

        .info-value {
          font-family: Source Han Sans;
          font-size: 15px;
          font-weight: normal;
          line-height: 20px;
          text-align: right;
          letter-spacing: normal;
          /* ☀️浅色模式/01_文字色/1级文字&深色图标level1_base_color */
          color: #171A1D;
        }
      }
    }
  }

  .bottom {
    height: 102px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 12px 16px;
    text-align: right;
  }
}
</style>
