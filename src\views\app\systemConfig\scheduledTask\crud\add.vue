﻿<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form :config="basicFormConfig" pageType="add" @onSubmit="onBasicSubmit" />
  </div>
</template>

<script setup>
import { sassTrue } from 'sass';
import { showToast } from 'vant'
import { useRoute } from 'vue-router'
import { ref, computed, getCurrentInstance } from 'vue'

const route = useRoute()
const { proxy } = getCurrentInstance()

// 当前选中的任务类型
const currentTaskType = ref('3') // 默认设置为发放补贴

// 动态获取用户标签文本的函数
const getUserLabelText = (taskType) => {
  const labelMap = {
    '3': '适用人员',  // 发放补贴
    '1': '适用人员',  // 扣除补贴
    '2': '清空人员',  // 清空待还
    '0': '预定人员',  // 餐食预定
    '4': '适用人员',  // 数据统计通知
    '5': '适用人员'   // 餐时预定通知
  }
  return labelMap[taskType] || '适用人员'
}

// 创建动态表单配置
const createDynamicFormConfig = () => {
  // 获取当前任务类型对应的标签文本
  const currentLabel = getUserLabelText(currentTaskType.value)

  return {
    button: {
      isShow: true,
    },
    postData: {
      // 如果有id参数，则为编辑模式
      ...(route.query.id ? { id: route.query.id } : {})
    },
    curl: {
      add: '', // 清空接口URL，让数据通过 onBasicSubmit 处理
      edit: '/schedule_task/post_modify', // 编辑接口
      info: '/schedule_task/get_info' // 获取详情接口（编辑时需要）
    },
    // 数据格式化函数 - 编辑模式下将后端 all_user 字段映射到前端字段
    format: (data) => {
      console.log('🔍 [编辑页面] format 函数被调用')
      console.log('🔍 [编辑页面] 原始后端数据:', JSON.stringify(data, null, 2))

      // 根据任务类型确定应该映射到哪个前端字段
      const taskType = String(data.cron_type || '')
      console.log('🔍 [编辑页面] 任务类型:', taskType)

      // 字段映射表（反向映射）
      const reverseFieldMappings = {
        '3': 'applicable_user_grant',    // 发放补贴
        '1': 'applicable_user_deduct',   // 扣除补贴
        '2': 'clear_user',               // 清空待还
        '0': 'booking_user'              // 餐食预定
      }

      // 检查是否有 all_user 字段
      console.log('🔍 [编辑页面] 是否包含 all_user 字段:', data.hasOwnProperty('all_user'))
      console.log('🔍 [编辑页面] all_user 值:', data.all_user)

      // 如果后端数据包含 all_user 字段，需要映射到对应的前端字段
      if (data.hasOwnProperty('all_user') && reverseFieldMappings[taskType]) {
        const frontendKey = reverseFieldMappings[taskType]

        // 确保数据类型正确 - yhc-radio-group 可能需要字符串类型
        const mappedValue = String(data.all_user)
        data[frontendKey] = mappedValue

        console.log('✅ [编辑页面] 反向字段映射成功:')
        console.log(`   all_user (${data.all_user}) → ${frontendKey} (${mappedValue})`)
        console.log('🔍 [编辑页面] 映射后的完整数据:', JSON.stringify(data, null, 2))
      } else {
        console.log('❌ [编辑页面] 字段映射失败:')
        console.log('   - 是否有 all_user:', data.hasOwnProperty('all_user'))
        console.log('   - 任务类型映射:', reverseFieldMappings[taskType])
      }

      // 注意：yhc-form 的 format 函数是直接修改 data 对象，不需要返回值
    },
    groupForm: [
      [0, 1],
      [1, 2],
      [2, 3],
      [3, 5],
      [5, 6],
      // [5, 6],
    ],
    form: [
      {
        label: "任务名称",
        key: "title",
        component: "yhc-input",
        type: "text",
        placeholder: "请输入",
        required: true,
        rules: [{ required: true, message: "请填写减免规则名称" }],
      },
      {
        label: "任务类型",
        key: "cron_type",
        component: "yhc-picker",
        // default: "3",
        onChange: (value) => {
          console.log('任务类型切换:', value, '→', getUserLabelText(String(value)));
          currentTaskType.value = String(value);
        },
        opts: {
          url: "",
          postData: {},
          merge: false,
          multiple: false,
          text_key: "title",
          contrast_key: "id",
          keyMap: "id",
          defaultList: [
            { id: 3, title: "发放补贴" },
            { id: 1, title: "扣除补贴" },
            { id: 2, title: "清空待还" },
            { id: 0, title: "餐食预定" },
            { id: 4, title: "数据统计通知" },
            { id: 5, title: "餐食预定通知" },
          ],
        },
        child: {
          showMode: 'show',
          map: {
            3: [
              {
                label: "定时给用户发放补贴",
                key: "yhc-desc",
              },
              {
                label: "适用人员",
                key: "applicable_user_grant",
                component: "yhc-radio-group",
                default: "0",
                options: [
                  { value: "0", label: "全部人员" },
                  { value: "1", label: "指定人员" }
                ],
                shape: "dot",
                // required: true,
                child: {
                  map: {
                    "1": [
                      {
                        label: "内部员工",
                        key: "userlst",
                        component: "yhc-select-user",
                      },
                      {
                        label: "外部员工",
                        key: "outsiderlst",
                        component: "yhc-select-user",
                      },

                    ],
                  },
                  form: [],
                },
              },
              {
                label: "发放金额",
                key: "money",
                component: "yhc-input",
                type: "number",
                placeholder: "请输入",
                "right-icon": "元",
                min: 0.01,
                decimalPlaces: true
              },
              {
                label: "备注",
                key: "desc",
                component: "yhc-input",
                placeholder: "请输入",
                type: "textarea",
                rows: 3,
                maxlength: 50,
                showWordLimit: true
              }
            ],
            1: [
              {
                label: "定时扣除用户补贴",
                key: "yhc-desc",
              },
              {
                label: "适用人员",
                key: "applicable_user_deduct",
                component: "yhc-radio-group",
                default: "0",
                options: [
                  { value: "0", label: "全部人员" },
                  { value: "1", label: "指定人员" }
                ],
                shape: "dot",
                // required: true,
                child: {
                  map: {
                    "1": [
                      {
                        label: "内部员工",
                        key: "userlst",
                        component: "yhc-select-user",
                      },
                      {
                        label: "外部员工",
                        key: "outsiderlst",
                        component: "yhc-select-user",
                      },

                    ],
                  },
                  form: [],
                },
              },
              {
                label: "扣除类型",
                key: "deduct_type",
                default: "0",
                component: "yhc-picker",
                placeholder: "请选择",
                opts: {
                  url: "",
                  postData: {},
                  merge: false,
                  multiple: false,
                  text_key: "title",
                  contrast_key: "id",
                  keyMap: "id",
                  defaultList: [
                    { id: "0", title: "定额扣除" },
                    { id: "1", title: "全额扣除" },
                  ]
                },
                child: {
                  showMode: true,
                  map: {
                    "0": [
                      {
                        label: "扣除金额",
                        key: "min_amount",
                        component: "yhc-input",
                        type: "number",
                        placeholder: "请输入",
                        "right-icon": "元",
                        // min:0.01,
                        decimalPlaces: true
                      },
                    ],
                  },
                  form: [],
                },
              },
              {
                label: "备注",
                key: "desc",
                component: "yhc-input",
                placeholder: "请输入",
                type: "textarea",
                rows: 3,
                maxlength: 50,
                showWordLimit: true
              }
            ],
            2: [
              {
                label: "定时扣除用户待还欠款",
                key: "yhc-desc",
              },
              {
                label: "清空人员",
                key: "clear_user",
                component: "yhc-radio-group",
                default: "0",
                options: [
                  { value: "0", label: "全部人员" },
                  { value: "1", label: "指定人员" }
                ],
                shape: "dot",
                child: {
                  map: {
                    "1": [
                      {
                        label: "内部员工",
                        key: "userlst",
                        component: "yhc-select-user",
                      },
                      {
                        label: "外部员工",
                        key: "outsiderlst",
                        component: "yhc-select-user",
                      },

                    ],
                  },
                  form: [],
                },
              },
              {
                label: "备注",
                key: "desc",
                component: "yhc-input",
                placeholder: "请输入",
                type: "textarea",
                rows: 3,
                maxlength: 50,
                showWordLimit: true
              }
            ],
            0: [
              {
                label: "定时餐食预定",
                key: "yhc-desc",
              },
              {
                label: "预定人员",
                key: "booking_user",
                component: "yhc-radio-group",
                default: "0",
                options: [
                  { value: "0", label: "全部人员" },
                  { value: "1", label: "指定人员" }
                ],
                shape: "dot",
                child: {
                  map: {
                    "1": [
                      {
                        label: "内部员工",
                        key: "userlst",
                        component: "yhc-select-user",
                      },
                      {
                        label: "外部员工",
                        key: "outsiderlst",
                        component: "yhc-select-user",
                      },
                      {
                        label: "餐厅",
                        key: "dininghall_id",
                        component: "yhc-picker",
                        // rules: [{ required: true, message: "请选择菜品分类" }],
                        LinkKeyConfig: [
                          {
                            key: "repast_id",
                            postData: {
                              dininghall_id: "dininghall_id",
                            },
                          },
                          {
                            key: "window_id",
                            postData: {
                              dininghall_id: "dininghall_id",
                            },
                          },
                        ],
                        opts: {
                          url: "dininghall/get_all",
                          postData: {},
                          merge: false,
                          multiple: false,
                          text_key: "title",
                          contrast_key: "id",
                          keyMap: "id",
                          defaultList: [],
                        },
                        card: {
                          //   num: "2",
                          // price: "2.00",
                          // desc: "id",
                          title: "title",
                          // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
                        },
                        child: {
                          showMode: true,
                          form: [
                            {
                              label: "餐时",
                              key: "repast_id",
                              component: "yhc-picker",
                              //   rules: [{ required: true, message: "请选择餐厅" }],
                              opts: {
                                url: "/mealtime/get_all",
                                postData: {},
                                merge: false,
                                multiple: false,
                                text_key: "title",
                                contrast_key: "id",
                                keyMap: "id",
                                defaultList: [],
                              },

                              card: {
                                title: "title",
                                desc: "start_time",
                                // price: "2.00",
                                // desc: "id",
                              },
                            },

                            {
                              label: "档口",
                              key: "window_id",
                              component: "yhc-picker",
                              //   rules: [{ required: true, message: "请选择餐厅" }],
                              opts: {
                                url: "/stall/get_all",
                                postData: {},
                                merge: false,
                                multiple: false,
                                text_key: "title",
                                contrast_key: "id",
                                keyMap: "id",
                                defaultList: [],
                              },
                              card: {
                                title: "title",
                              },
                            },
                          ],
                        },
                      },
                      {
                        label: "考勤规则",
                        key: "attendance_rule_title",
                        component: "yhc-picker",
                        placeholder: "请选择",
                        card: {
                          title: "rule_name",
                        },
                        opts: {
                          url: "/attendance_rule/get_all",
                          postData: {},
                          merge: false,
                          multiple: false,
                          text_key: "rule_name",
                          contrast_key: "id",
                          keyMap: 'id',
                          defaultList: []
                        },
                      },
                    ],
                  },
                  form: [],
                },
              },

            ],
            4: [
              {
                label: "通知接收人",
                key: "userlst",
                component: "yhc-select-user",
              },
              {
                label: "餐厅",
                key: "dininghall_id",
                component: "yhc-picker",
                // rules: [{ required: true, message: "请选择菜品分类" }],
                LinkKeyConfig: [
                  {
                    key: "repast_id",
                    postData: {
                      dininghall_id: "dininghall_id",
                    },
                  },
                  {
                    key: "window_id",
                    postData: {
                      dininghall_id: "dininghall_id",
                    },
                  },
                ],
                opts: {
                  url: "dininghall/get_all",
                  postData: {},
                  merge: false,
                  multiple: false,
                  text_key: "title",
                  contrast_key: "id",
                  keyMap: "id",
                  defaultList: [],
                },
                card: {
                  //   num: "2",
                  // price: "2.00",
                  // desc: "id",
                  title: "title",
                  // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
                },
                child: {
                  showMode: true,
                  form: [
                    {
                      label: "餐时",
                      key: "repast_id",
                      component: "yhc-picker",
                      //   rules: [{ required: true, message: "请选择餐厅" }],
                      opts: {
                        url: "/repast/get_all",
                        postData: {},
                        merge: false,
                        multiple: false,
                        text_key: "title",
                        contrast_key: "id",
                        keyMap: "id",
                        defaultList: [],
                      },

                      card: {
                        title: "title",
                        desc: "start_time",
                        // price: "2.00",
                        // desc: "id",
                      },
                    },

                    {
                      label: "档口",
                      key: "window_id",
                      component: "yhc-picker",
                      //   rules: [{ required: true, message: "请选择餐厅" }],
                      opts: {
                        url: "/window/get_all",
                        postData: {},
                        merge: false,
                        multiple: false,
                        text_key: "title",
                        contrast_key: "id",
                        keyMap: "id",
                        defaultList: [],
                      },
                      card: {
                        title: "title",
                      },
                    },
                  ],
                },
              },
              {
                label: "统计数据",
                key: "data_keys",
                component: "yhc-picker",
                placeholder: "请选择",
                card: {
                  title: "title",
                },
                opts: {
                  url: "",
                  postData: {},
                  merge: false,
                  multiple: true,
                  text_key: "title",
                  contrast_key: "id",
                  keyMap: {},
                  defaultList: [
                    { id: 1, title: "预定份数" },
                    { id: 2, title: "消费人数" },
                    { id: 3, title: "营收统计" },
                  ]
                },
              },

            ],
            5: [
              {
                label: "通知接收人",
                key: "userlst",
                component: "yhc-select-user",
              },
              {
                label: "餐厅",
                key: "dininghall_id",
                component: "yhc-picker",
                // rules: [{ required: true, message: "请选择菜品分类" }],
                LinkKeyConfig: [
                  {
                    key: "repast_id",
                    postData: {
                      dininghall_id: "dininghall_id",
                    },
                  },
                  {
                    key: "window_id",
                    postData: {
                      dininghall_id: "dininghall_id",
                    },
                  },
                ],
                opts: {
                  url: "dininghall/get_all",
                  postData: {},
                  merge: false,
                  multiple: false,
                  text_key: "title",
                  contrast_key: "id",
                  keyMap: "id",
                  defaultList: [],
                },
                card: {
                  //   num: "2",
                  // price: "2.00",
                  // desc: "id",
                  title: "title",
                  // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
                },
                child: {
                  showMode: true,
                  form: [
                    {
                      label: "餐时",
                      key: "repast_id",
                      component: "yhc-picker",
                      //   rules: [{ required: true, message: "请选择餐厅" }],
                      opts: {
                        url: "/repast/get_all",
                        postData: {},
                        merge: false,
                        multiple: false,
                        text_key: "title",
                        contrast_key: "id",
                        keyMap: "id",
                        defaultList: [],
                      },

                      card: {
                        title: "title",
                        desc: "start_time",
                        // price: "2.00",
                        // desc: "id",
                      },
                    },

                    {
                      label: "档口",
                      key: "window_id",
                      component: "yhc-picker",
                      //   rules: [{ required: true, message: "请选择餐厅" }],
                      opts: {
                        url: "/window/get_all",
                        postData: {},
                        merge: false,
                        multiple: false,
                        text_key: "title",
                        contrast_key: "id",
                        keyMap: "id",
                        defaultList: [],
                      },
                      card: {
                        title: "title",
                      },
                    },
                  ],
                },
              },
            ],
          },
          form: [],
        },
      },
      {
        label: "重复",
        key: "cycle",
        component: "yhc-picker",
        placeholder: "请选择",
        card: {
          title: "title",
        },
        opts: {
          url: "",
          postData: {},
          merge: false,
          multiple: false,
          text_key: "title",
          contrast_key: "id",
          keyMap: "id",
          defaultList: [
            { id: 1, title: "不重复" },
            { id: 2, title: "每天" },
            { id: 3, title: "每周" },
            { id: 4, title: "每月" },
          ]
        },
      },
      {
        label: '执行时间',
        key: "start_time",
        component: "yhc-picker-date",
        placeholder: "请输入",
        required: true,
        rules: [{ required: true, message: "请填写执行时间" }],
      },
      {
        label: '终止时间',
        key: "end_time",
        component: "yhc-picker-date",
        placeholder: "请输入",
        required: true,
        rules: [{ required: true, message: "请填写执行时间" }],
      },
      {
        label: "启动时间须>当前时间，终止时间须>启动时间",
        component: "yhc-desc",
      },

    ]
  }
}

// 使用响应式配置
const basicFormConfig = computed(() => createDynamicFormConfig())

const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改规则' : '新增规则',
  });
};
setRightA()

// 字段映射函数 - 将前端字段名映射为后端需要的统一字段名
const mapFieldsForBackend = (formData) => {
  const mappedData = { ...formData }

  // 字段映射表
  const fieldMappings = {
    'applicable_user_grant': 'all_user',    // 发放补贴 → all_user
    'applicable_user_deduct': 'all_user',   // 扣除补贴 → all_user
    'clear_user': 'all_user',               // 清空待还 → all_user
    'booking_user': 'all_user'              // 餐食预定 → all_user
  }

  // 执行字段映射
  Object.keys(fieldMappings).forEach(frontendKey => {
    if (mappedData.hasOwnProperty(frontendKey)) {
      const backendKey = fieldMappings[frontendKey]
      mappedData[backendKey] = mappedData[frontendKey]
      delete mappedData[frontendKey] // 删除原字段

      console.log(`字段映射: ${frontendKey} → ${backendKey}`, mappedData[backendKey])
    }
  })

  return mappedData
}

// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('原始表单数据:', data)

  // 执行字段映射
  const mappedData = mapFieldsForBackend(data)

  console.log('映射后的数据:', mappedData)
  console.log('后端将接收到统一的 all_user 字段:', mappedData.all_user)

  // 判断是否为编辑模式
  const isEditMode = route.query.id && route.query.id !== ''
  const apiUrl = isEditMode ? '/schedule_task/post_modify' : '/schedule_task/post_add'
  const successMessage = isEditMode ? '计划任务修改成功' : '计划任务创建成功'

  console.log('提交模式:', isEditMode ? '编辑模式' : '新增模式')
  console.log('API接口:', apiUrl)

  // 如果是编辑模式，需要添加id参数
  if (isEditMode) {
    mappedData.id = parseInt(route.query.id, 10)
    console.log('编辑模式 - 任务ID:', mappedData.id)
  }

  // 调用后端API提交映射后的数据
  proxy.$post(apiUrl, mappedData)
    .then((res) => {
      if (res.code === 200) {
        showToast(successMessage)
        // 清空本地缓存
        localStorage.removeItem('/systemConfig/scheduledTask/add')
        setTimeout(() => {
          // 返回上一页
          proxy.$router.go(-1)
        }, 1500)
      } else {
        throw res.msg
      }
    })
    .catch((err) => {
      console.error('提交失败:', err)
      showToast(err || '提交失败，请重试')
    })
}
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  height: 100%;
  // background: #f7f8fa;
  padding-bottom: 100px;
}
</style>
