<template>
  <van-search v-model="searchValue" placeholder="搜索" shape="round"/>

  <div class="window-info" v-if="data.typeList.length">

    <div class="left">
      <div :class="`repast-time ${i === data.curTypeIndex ? 'active-repast' : ''}`" v-for="(item, i) in data.typeList"
           :key="i" @click="onTypeClick(i)">
        <!-- <div class="select-line" v-show="i === data.curTypeIndex"></div> -->
        <div class="text-info">
          <div class="icon">
          </div>
          <div class="repast-text">{{ item.category_title }}</div>
          <!-- 数量徽标 -->
          <div class="category-badge" v-if="getCategoryCount(item) > 0 && item.category_title != '全部'">
            {{ getCategoryCount(item) }}
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="window-dish" v-for="(item, i) in data.dishList" :key="item.id + i">
        <van-image v-if="item.image" width="80" height="80" radius="8" :src="item.image ||
          'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
          "/>
        <div v-else style="
            width: 80px;
            height: 80px;
            border-radius: 8px;
            background: #007fff;
            color: #fff;
            font-size: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
          ">
          {{ item.title[0] }}
        </div>
        <div class="info-block">
          <div>{{ item.title }}</div>
          <!-- <div v-if="item.stock_text" style="margin: 2px 0; font-size: 10px; color: rgba(23, 26, 29, 0.4)">
            库存：{{ item.stock_text }}
          </div>
          <div v-if="item.stock_limit_text" style="margin: 2px 0; font-size: 10px; color: rgba(23, 26, 29, 0.4)">
            限购：{{ item.stock_limit_text }}
          </div> -->
          <div style="font-size: 13px; color: #969799;">
            {{ item.desc }}
          </div>
          <div class="bottom">
            <!-- <div style="color: rgba(23, 26, 29, 0.4); font-size: 12px"> -->
            <span style="color: #000">
                ￥<span style="font-size: 17px">{{ formatPrice(item.price).integer }}</span><span
                style="font-size: 13px">{{ formatPrice(item.price).decimal }}</span>
              </span>
            <!-- </div> -->
            <van-stepper v-model.number="item.count" theme="round" min="0" default-value="" button-size="22" allow-empty
                         :show-minus="!!item.count" :show-input="!!item.count" :max="item.stock_limit_text == '无限' && item.stock_text == '无限'
                ? undefined
                : item.quota
                  ? undefined
                  : item.quota || item.stock
                " @change="onChange(item)"/>
          </div>
        </div>
      </div>
    </div>
    <div class="goods-bar">
      <div class="total">
        <van-icon size="34" :name="getImageUrl(`/images/common/goodscar.png`)" color="#1989fa"
                  :badge="data.selectList.size" @click="data.showBottom = true"/>
        <span style="margin-left: 16px; font-size: 14px">已选</span>
        <span style="font-size: 17px">{{ data.selectList.size }}个</span>
      </div>
      <van-button round type="primary" @click="onSettlementClick">去下单</van-button>
    </div>
    <!-- 底部弹出 -->
    <van-popup v-model:show="data.showBottom" position="bottom" :style="{ height: '50%' }">
      <div class="popup-top-block">
        <van-icon size="24" name="delete-o"/>
        <span @click="onGoodsClear" style="margin-left: 8px">清空购物车</span>
      </div>
      <van-empty v-if="!data.selectList.size" image-size="100" description="未选择菜品~" class="popup-list"/>
      <div v-else class="right popup-list" style="margin-left: 0px">
        <div class="window-dish" v-for="(item, i) in data.selectList" :key="item.id + i">
          <van-image v-if="item.image" width="90" height="90" radius="4" :src="item.image ||
            'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
            "/>
          <div v-else style="
              width: 90px;
              height: 90px;
              border-radius: 4px;
              background: #007fff;
              color: #fff;
              font-size: 36px;
              display: flex;
              align-items: center;
              justify-content: center;
            ">
            {{ item.title[0] }}
          </div>
          <div class="info-block">
            <div>{{ item.title }}</div>
            <div v-if="item.stock_text" style="
                margin: 2px 0;
                font-size: 10px;
                color: rgba(23, 26, 29, 0.4);
              ">
              库存：{{ item.stock_text }}
            </div>
            <div v-if="item.stock_limit_text" style="
                margin: 2px 0;
                font-size: 10px;
                color: rgba(23, 26, 29, 0.4);
              ">
              限购：{{ item.stock_limit_text }}
            </div>
            <div style="
                margin: 2px 0;
                font-size: 10px;
                color: rgba(23, 26, 29, 0.4);
              ">
              {{ item.desc }}
            </div>
            <div class="bottom">
              <div style="color: rgba(23, 26, 29, 0.4); font-size: 12px">
                <span style="color: #000">
                  ￥<span style="font-size: 14px">{{ formatPrice(item.price).integer }}</span><span
                    style="font-size: 13px">{{ formatPrice(item.price).decimal }}</span>
                </span>/份
              </div>
              <van-stepper v-model.number="item.count" theme="round" min="0" default-value="" button-size="22"
                           allow-empty :show-minus="!!item.count" :show-input="!!item.count" :max="item.stock_limit_text == '无限' && item.stock_text == '无限'
                  ? undefined
                  : item.quota || item.stock
                  " @change="onChange(item)"/>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
  <van-empty v-else description="未发布菜品~" style="height: 200px">
  </van-empty>
</template>
<script setup>
import {ref, reactive, getCurrentInstance, computed} from "vue";
import {showNotify, showToast, closeNotify, ActionSheet} from "vant";
import {useLoginStore} from "@/store/dingLogin";

const app = useLoginStore();
app.isTakeout = undefined;
const route = useRoute();
const router = useRouter();
const {proxy} = getCurrentInstance();
let data = reactive({
  showBottom: false,
  curTypeIndex: 0,
  selectList: app.orderDishSelectList || new Set(),
  typeList: [],
  dishList: [],
  // 原始分类数据（不包含"全部"选项）
  originalTypeList: [],
  // 所有菜品数据（用于"全部"选项）
  allDishList: [],
});

function getImageUrl(name) {
  return new URL(name, import.meta.url).href;
}

// 计算分类菜品数量
const getCategoryCount = (category) => {
  if (!category || !category.dishess) return 0;
  return category.dishess.length;
};

// 计算总菜品数量
const getTotalCount = () => {
  return data.allDishList.length;
};

// 格式化价格显示，小数点后字体更小
const formatPrice = (price) => {
  const priceStr = price.toString();
  const parts = priceStr.split('.');

  if (parts.length === 1) {
    // 没有小数点，直接返回整数部分
    return {
      integer: parts[0],
      decimal: ''
    };
  } else {
    // 有小数点，分别返回整数和小数部分
    return {
      integer: parts[0],
      decimal: '.' + parts[1]
    };
  }
};
const onSettlementClick = () => {
  if (!data.selectList.size) {
    showToast(JSON.stringify("请选择菜品"));
  } else {
    app.orderDishSelectList = data.selectList;
    router.push({
      path: "/confirmOrder",
      query: route.query,
    });
  }
};
let total = computed(() => {
  let total = 0;
  for (let el of data.selectList) {
    total += el.price * 1 * (el.count * 1);
  }
  return total.toFixed(2);
});
const onTypeClick = (i) => {
  data.curTypeIndex = i;
  // 如果点击的是"全部"选项（索引为0），显示所有菜品
  if (i === 0) {
    data.dishList = data.allDishList;
  } else {
    // 否则显示对应分类的菜品（需要减1因为插入了"全部"选项）
    data.dishList = data.originalTypeList[i - 1].dishess;
  }
};
const onGoodsClear = (i) => {
  for (let el of data.selectList) {
    el.count = 0;
  }
  data.selectList.clear();
};
const onChange = (item) => {
  if (item.count <= 0) {
    let ls = [];
    data.selectList.forEach((el) => {
      if (item.id == el.id) {
        ls.push(el);
      }
    });
    ls.forEach((el) => {
      data.selectList.delete(el);
    });
  } else if (item.count > 0) {
    let ls = [];
    data.selectList.forEach((el) => {
      if (item.id == el.id) {
        el.count = item.count;
        ls.push(el);
      }
    });
    if (!ls.length) {
      data.selectList.add(item);
    }
  }
};
const getMenu = (item) => {
  console.log(item, 'qwqwqwqwqw');
  proxy
      .$get("dishes_release/get_info", {
        mealtime_id: item.repast_id,
        date: item.date,
        stall_id: item.id,
        dininghall_id: localStorage.getItem("dininghall"),
      })
      .then((res) => {
        if (res.code === 200) {
          res = res.data;
          console.log(res, 'res');
          if (JSON.parse(res.dishess).length) {
            // 从菜品数据中提取分类信息
            const dishesData = JSON.parse(res.dishess);
            const categoryMap = new Map();
            let allDishes = [];

            // 遍历菜品，按分类组织数据并处理已选择的菜品
            dishesData.forEach((dish) => {
              // 处理已选择的菜品数量
              if (data.selectList.size) {
                data.selectList.forEach((se) => {
                  if (dish.id == se.id) {
                    dish.count = se.count;
                  }
                });
              }

              // 添加到所有菜品列表
              allDishes.push(dish);

              const categoryId = dish.category_id;
              const categoryTitle = dish.category_title;

              if (!categoryMap.has(categoryId)) {
                categoryMap.set(categoryId, {
                  category_id: categoryId,
                  category_title: categoryTitle,
                  dishess: []
                });
              }

              // 将菜品添加到对应分类
              categoryMap.get(categoryId).dishess.push(dish);
            });

            // 将Map转换为数组
            const categories = Array.from(categoryMap.values());

            // 保存原始分类数据
            data.originalTypeList = categories;
            data.allDishList = allDishes;

            // 创建包含"全部"选项的分类列表
            const allCategory = {
              category_id: 'all',
              category_title: '全部',
              dishess: allDishes
            };

            // 将"全部"选项插入到分类列表的开头
            data.typeList = [allCategory, ...categories];

            // 默认显示所有菜品
            data.dishList = allDishes;
            data.curTypeIndex = 0; // 默认选中"全部"
          }
        } else {
          throw res.msg;
        }
      })
      .catch((err) => {
        console.log(err);
        showToast(err);
      })
      .finally(() => {
      });
};
getMenu(route.query);
</script>
<style scoped lang="scss">
.window-info {
  position: relative;

  .left {
    position: absolute;
    left: 0;
    top: 0;
    width: 80px;
    overflow: scroll;
    height: 100vh;
    padding-bottom: 120px;

    .repast-time {
      width: 80px;
      height: 60px;
      display: flex;
      position: relative;

      .select-line {
        width: 3px;
        background: #007fff;
      }

      .text-info {
        flex: 1;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        font-size: 14px;
        position: relative;
        width: 80px;
        height: 60px;

        .repast-text {
          font-size: 14px;
          margin-top: 10px;
        }

        .category-badge {
          position: absolute;
          top: 14px;
          right: 10px;
          min-width: 16px;
          height: 16px;
          padding: 0 4px;
          font-size: 10px;
          line-height: 16px;
          border-radius: 8px;
          background-color: #ff4444;
          color: #fff;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }
      }
    }

    .active-repast {
      background: #fff;
    }
  }

  .right {
    margin-left: 74px;
    height: 100vh;
    overflow: scroll;
    background: #fff;
    padding-bottom: 120px;

    .window-dish {
      display: flex;
      justify-content: space-between;
      padding: 12px;

      .info-block {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 8px;

        .bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
    }
  }

  .goods-bar {
    padding: 0 12px;
    position: fixed;
    bottom: 16px;
    left: 0;
    right: 0;
    height: 58px;
    border-radius: 29px;
    background: #ffffff;
    box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.16);
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 16px;

    .total {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .popup-top-block {
    color: rgba(23, 26, 29, 0.6);
    font-size: 12px;
    display: flex;
    align-items: center;
    padding: 18px 0;
    margin: 0 12px;
    border-bottom: 1px solid #f6f6f6;
    justify-content: flex-end;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }

  .popup-list {
    height: calc(100% - 59px);
    margin-top: 59px;
  }
}
</style>
