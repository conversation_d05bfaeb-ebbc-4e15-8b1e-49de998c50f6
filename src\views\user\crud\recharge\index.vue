<template>
  <div class="wrapper-recharge">
    <van-space fill direction="vertical">
      <van-cell-group inset style="padding: 4px 0;">
        <van-field
          v-model="money"
          type="number"
          name="money"
          label="充值金额"
          placeholder="请输入充值金额"
          label-align="top"
          clearable
          clear-trigger="always"
          :rules="[{ required: true, message: '请填写充值金额' }]"
        />
        <!-- 当金额小于20时显示提示信息 -->
        <div v-if="showMinAmountTip" class="min-amount-tip">
          <div class="tip-message">充值金额最低20元，请重新输入</div>
        </div>
      </van-cell-group>
      <span class="icon">¥</span>
      <van-checkbox-group v-model="checked">
        <van-cell-group inset>
          <van-cell
            v-for="(item, index) in list"
            clickable
            :key="index"
            :title="item.title"
          >
            <template #title class="title">
            <div class="base-flex">
              <img src="/public/img/alipay.svg" style="width:32px;height:32px;margin-right:8px;" alt="" />
              <span>{{ item.title }}</span>
            </div>
            </template>
            <template #right-icon>
               <van-checkbox
                :name="item.title"
                label-disabled
                @click.stop="checkedboxClick"
                :ref="(el) => (checkboxRefs[index] = el)"
              /> 
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
      <div style="margin: 16px" v-if="money">
        <van-button
          :loading="loading"
          :size="20"
          block
          color="#1989FA"
          @click="onSubmit"
        >
          充值
        </van-button>
      </div>
      <div style="margin: 16px" v-else>
        <van-button
          :loading="loading"
          :size="20"
          block
          disabled
          color="#dcdee0"
          @click="onSubmit"
        >
          充值
        </van-button>
      </div>
    </van-space>
  </div>
</template>
<script setup>
import { ref, nextTick, computed, getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
import { showToast, showDialog } from "vant";
const money = ref("");
const checked = ref([]);
const checkedbox = ref(true);
const loading = ref(false);
const checkboxRefs = ref([]);

// {{ AURA-X: Add - 添加最低金额提示逻辑. Approval: 寸止(ID:pending). }}
// 计算属性：当金额小于20且不为空时显示提示
const showMinAmountTip = computed(() => {
  const amount = parseFloat(money.value);
  return money.value !== "" && !isNaN(amount) && amount < 20;
});

// 清除金额输入
const clearAmount = () => {
  money.value = "";
};
const list = [
  {
    title: "支付宝支付",
    icon: "alipay",
  },
  // {
  //   title: "微信支付",
  //   icon: "wechat",
  // }
];
const checkedboxClick = (val) => {
  checked.value = ["支付宝支付"];
};
const onSubmit = (values) => {
  showDialog({
    title: "充值",
    message: `充值金额：${money.value}元\n您将要跳转至支付宝进行充值支付，是否继续？`,
  })
    .then(() => {
      post();
    })
    .catch(() => {
      // on cancel
    });
};
const toggle = (index) => {
  checkboxRefs.value[0].toggle();
};
nextTick(toggle);
const post = () => {
  loading.value = true;
  proxy
    .$post(import.meta.env.VITE_APP_USER_API + "recharge/post_add", {
      money: money.value,
      pay_type: 0,
    })
    .then((res) => {
      if (!res.errcode) {
        res = res.result;
        window.location.href = res.qr_code;
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    })
    .finally(() => {
      loading.value = false;
    });
};
</script>
<style lang="scss" >
.wrapper-recharge {
  width: 100%;
  min-height: 100vh;
  padding-top: 16px;
  position: relative;
  .van-field__label { 
    color: #171A1D;
    font-size: 16px;
    margin-bottom: 16px;
  } 
  
  .van-field__control {
    font-size: 32px;
    margin-left: 22px;
    line-height: 38px;
    // font-weight: 600;
    .van-field__control-input-placeholder { 
       font-weight: 500;
    }
  }
  .icon {
    position: absolute;
    left: 33px;
    top: 83px;
    font-size: 20px;
    color: #171A1D;
    line-height: 24px;
  }
  .van-icon {
    margin-right: 8px;
  }
  .van-cell{
    color: #171A1D;
    font-size: 16px;
    padding: 12px 16px;
    line-height: 22px;
   
  }
  .van-checkbox__icon--checked .van-icon{
    background-color: #80bfff;
    border-color: #80bfff;
  }
  .base-flex{
    display: flex;
    align-items: center;
    color: #171A1D;
  }
  .van-button{
    border-radius: 22px;
  }
  .min-amount-tip {
    margin: 8px 16px 0;

    .tip-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #FFF7E6;
      border: 1px solid #FFD591;
      border-radius: 6px;
      padding: 8px 12px;
      margin-bottom: 4px;

      .tip-amount {
        font-size: 16px;
        font-weight: 500;
        color: #FA8C16;
      }

      .tip-close {
        font-size: 14px;
        color: #8C8C8C;
        cursor: pointer;

        &:hover {
          color: #595959;
        }
      }
    }

    .tip-message {
      font-size: 14px;
      line-height: 20px;
      color: #FF5219;
      margin-top: -10px;
      padding-bottom: 10px;
    }
  }
}
</style>
