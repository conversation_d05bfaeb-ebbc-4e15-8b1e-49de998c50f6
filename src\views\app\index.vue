<template>
  <div class="wrapper-app" v-if="initStatus">
    <div class="top-block">
      <van-dropdown-menu>
        <van-dropdown-item v-model="curDininghall" :options="option" @change="dininghallChange" />
      </van-dropdown-menu>
      <van-icon name="apps-o" class="van-haptics-feedback" size="24" @click="toApps" />
      <!-- <van-icon name="setting-o" class="van-haptics-feedback" size="24" /> -->
    </div>
    <!-- <van-search v-model="data.searchValue" placeholder="请输入搜索关键词" :clearable="false" @search="onSearch"
      @update:model-value="onSearch" /> -->
    <div v-if="data.isSearch">
      <div class="nav-block" v-if="searchList.length">
        <div class="nav-item van-haptics-feedback" v-for="(item, i) in searchList" :key="i" @click="click(item)">
          <div class="icon-wrap" :style="setBackground(item.color)">
            <img class="icons" :src="`/icons/${item.icon}.png`" alt="" :style="{ width: '100%', height: '100%' }" />
          </div>
          <div>{{ item.text }}</div>
        </div>
      </div>
      <van-empty v-else image-size="80" description="没有找到模块~" />
    </div>
    <div v-else>
      <div class="nav-block" v-if="navList.show[0].list.length">
        <div class="nav-item van-haptics-feedback" v-for="(item, i) in navList.show[0].list" :key="i + item.text"
          @click="click(item)">
          <div class="icon-wrap" :style="setBackground(item.color)">
            <img class="icons" :src="`/icons/${item.icon}.png`" alt="" style="width: 100%" />
          </div>
          <div>{{ item.text }}</div>
        </div>
      </div>
      <div class="tabs-block tabs-block-block" v-if="curTab">
        <van-tabs ref="tabs" v-model:active="tabsActive" line-width="16px" color="#000000" :sticky="config.tabs.sticky"
          shrink border="true" :swipeable="config.tabs.swipeable" @click-tab="onClickTab">
          <van-tab v-for="(tab, i) in config.tabs.list" :title="tab.text" :key="i">
            <div v-if="curTab && curTab.length">
              <div v-for="(el, i) in curTab" :key="i + el.title">
                <div v-if="el.list.length" style="
                    font-size: 14px;
                    color: #969799;
                    margin: 16px 0 0 16px;
                  ">
                  {{ el.title }}
                </div>
                <div class="nav-block tab-nav-block" v-if="el.list.length">
                  <div class="nav-item van-haptics-feedback" v-for="(item, index) in el.list" :key="index + item.text"
                    @click="click(item)">
                    <div class="icon-wrap">
                      <img class="icons" :src="`/icons/${item.icon}.svg`" alt=""
                        :style="{ width: '100%', height: '100%' }" />
                    </div>
                    <div v-html="formatText(item.text)"></div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else>
              <van-empty image-size="80" description="没有模块啦~" />
            </div>
          </van-tab>
        </van-tabs>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, watch, getCurrentInstance, computed, onMounted } from "vue";
import { showToast } from "vant";
import { routerList, set_permission, router_list } from "@/views/home/<USER>/routerList";
const recordsTab = ref();
let initStatus = ref(false);
import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();
const { proxy } = getCurrentInstance();
const router = useRouter();
let config = {
  curl: {
    ls: "dininghall/get_all",
    dininghallChange: "dininghall/post_toggle",
  },
  postData: {},
  tabs: {
    isShow: true,
    sticky: false,
    swipeable: false,
    list: [
      {
        text: "基础配置",
        key: "base",
      },
      // {
      //   text: "财务管理",
      //   key: "finance",
      // },
      {
        text: "访客",
        key: "visiter",
      },
      {
        text: "商品管理",
        key: "goods",
      },
      {
        text: "进销存",
        key: "purs",
      },
      {
        text: "其他",
        key: "other",
      },
    ],
  },
};

let navList = {
  show: [
    {
      title: "",
      list: [

      ],
    },
  ],
  visiter: [
    {
      title: "访客充值",
      list: [
        {
          text: "面包屑",
          url: "/bread",
          icon: "datav",
          color: "rgba(0,186,70, 1.0)",
        },
      ],
    },
    {
      title: "访客扣款",
      list: [
        {
          text: "表单",
          url: "/form",
          icon: "datav",
          color: "rgba(0,186,70, 1.0)",
        },
      ],
    },
    {
      title: "访客信息",
      list: [
        {
          text: "面包屑",
          url: "/bread",
          icon: "datav",
          color: "rgba(0,186,70, 1.0)",
        },
      ],
    },
  ],
  base: [
    {
      title: "Demo",
      list: [
        {
          text: "面包屑面包屑",
          url: "/bread",
          icon: "datav",
          color: "rgba(0,186,70, 1.0)",
        },
        {
          text: "表单",
          url: "/form",
          icon: "datav",
          color: "rgba(0,186,70, 1.0)",
        },
        {
          text: "分段器",
          url: "/steps",
          icon: "datav",
          color: "rgba(0,186,70, 1.0)",
        },
        {
          text: "骨架屏演示骨架屏演示",
          url: "/skeleton-demo",
          icon: "datav",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "表格演示",
          url: "/table-demo",
          icon: "datav",
          color: "rgba(34,139,34, 1.0)",
        },
        {
          text: "应用市场",
          url: "/app-market",
          icon: "datav",
          color: "rgba(138,43,226, 1.0)",
        },
      ],
    },
    {
      title: "餐厅管理",
      list: [
        {
          text: "菜品发布",
          url: "/dishRelease",
          icon: "foodRelease",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "档口管理",
          url: "/stall",
          icon: "stall",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "餐时配置",
          url: "/menuTime",
          icon: "mealTime",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "餐时组",
          url: "/menuTimeGroup",
          icon: "mealTimeGroup",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "餐费设置",
          url: "/menuMeal",
          icon: "foodCost",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "消费规则",
          url: "/consumeRule",
          icon: "dine",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "菜品分类",
          url: "/dishSort",
          icon: "foodClassify",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "菜品配置",
          url: "/dishConfig",
          icon: "foodAdd",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "包间配置",
          url: "/roomConfig",
          icon: "privateRoom",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "场所管理",
          url: "/venueSetup",
          icon: "place",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "公费预定",
          url: "/publicRese",
          icon: "order",
          color: "rgba(255,140,0, 1.0)",
        },
      ],
    },
    {
      title: "系统管理",
      list: [
        {
          text: "基础配置",
          url: "/systemConfig/basicConfiguration",
          icon: "systemSet",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "权限组",
          url: "/systemConfig/authority",
          icon: "authority",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "设备管理",
          url: "/systemConfig/deviceManagement",
          icon: "device",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "减免规则",
          url: "/systemConfig/reduceRule",
          icon: "datav",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "企业文化与驾驶舱",
          url: "/systemConfig/corporateCulture",
          icon: "datav",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "考勤规则",
          url: "/systemConfig/attendanceRule",
          icon: "datav",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "系统公告",
          url: "/announcement",
          icon: "announcement",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "计划任务",
          url: "/scheduledTask",
          icon: "datav",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "审计日志",
          url: "/auditLog",
          icon: "log",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "联系人",
          url: "/contacts",
          icon: "datav",
          color: "rgba(0,123,255, 1.0)",
        },
        {
          text: "成本中心",
          url: "/costCenter",
          icon: "costCenter",
          color: "rgba(255,140,0, 1.0)",
        }
      ]
    },
    {
      title: "预定",
      list: [
        {
          text: "包间预定",
          url: "/reserve/roomReserve",
          icon: "datav",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "公费预定",
          url: "/reserve/publicReserve",
          icon: "datav",
          color: "rgba(255,140,0, 1.0)",
        },
      ]
    },
    {
      title: "财务",
      list: [
        {
          text: "发放补贴",
          url: "/provideSubsidies",
          icon: "recharge",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "扣除补贴",
          url: "/deductSubsidies",
          icon: "deduction",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "清空待还",
          url: "/clearDebt",
          icon: "clear",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "交易流水",
          url: "/finance/runningTab",
          icon: "balance",
          color: "rgba(255,140,0, 1.0)",
        },
      ]
    }

  ],
  finance: [
    {
      title: "财务充值",
      list: [
        {
          text: "表单",
          url: "/form",
          icon: "datav",
          color: "rgba(0,186,70, 1.0)",
        },
      ],
    },
    {
      title: "财务扣款",
      list: [
        {
          text: "表单",
          url: "/form",
          icon: "datav",
          color: "rgba(0,186,70, 1.0)",
        },
      ],
    },
    {
      title: "财务记录",
      list: [

      ],
    },
  ],
  goods: [
    {
      title: "商品信息",
      list: [
        {
          text: "表单",
          url: "/form",
          icon: "datav",
          color: "rgba(0,186,70, 1.0)",
        },
      ],
    },
    {
      title: "商品记录",
      list: [
        {
          text: "表单",
          url: "/form",
          icon: "datav",
          color: "rgba(0,186,70, 1.0)",
        },
      ],
    },
  ],
  purs: [
    {
      title: "食材信息",
      list: [
      ],
    },
    {
      title: "采购出库",
      list: [
      ],
    },
  ],
  other: [
    {
      title: "其他配置",
      list: [
      ],
    },
    {
      title: "其他信息",
      list: [
      ],
    },
    {
      title: "餐厅反馈",
      list: [
      ],
    },
  ],
};

const formatText = (text) => {
  if (text.length > 4) {
    return text.substring(0, 4) + '<br>' + text.substring(4);
  }
  return text;
};
delete app.visiterList;
delete app.all_visitor;
delete app.money;
delete app.desc;
delete app.type;
delete app.type_title;

const tabs = ref(null);
const tabsActive = ref(Number(window.sessionStorage.getItem("tabNum")));
onMounted(() => {

  let tabNum = window.sessionStorage.getItem("tabNum");
  if (tabNum || tabNum == 0) {
    if (config.tabs.list[tabNum]) {
      curTab.value = navList[config.tabs.list[tabNum].key];
    }
  }
});
const curDininghall = ref();
const curTab = ref({});
const searchList = ref([]);
const data = reactive({
  searchValue: "",
  isSearch: false,
});
let option = [];
const click = (item) => {
  if (!item.url) {
    showToast(`${item.text}未设置路径`);
    return;
  }
  window.sessionStorage.setItem("tabNum", tabsActive.value);
  window.sessionStorage.setItem("tabCurTab", JSON.stringify(curTab.value));



  router.push(item.url);
};
const setBackground = (color) => `background:${color.replace("1.0", "0.1")}`;
const onSearch = (e) => {
  data.isSearch = !!e;
  searchList.value = [];
  for (let key in navList) {
    navList[key].forEach((block) => {
      searchList.value.push(
        ...block.list.filter((nav) => nav.text.includes(e))
      );
    });
  }
};
const toApps = () => {
  router.push('/app-market')
}
const configTabsList = JSON.parse(JSON.stringify(config));
const navListCopy = JSON.parse(JSON.stringify(navList));
function handlePremission(op) {
  for (let key in navListCopy) {
    navListCopy[key].forEach((block, i) => {
      navList[key][i].list = block.list.filter((nav) =>
        op.find((route) => route.url == nav.url)
      );
      console.log(key, i, navList[key][i].list, block);
    });
  }
  config.tabs.list = configTabsList.tabs.list.filter(
    (el) => !navList[el.key].every((list) => !list.list.length)
  );

  if (JSON.parse(window.sessionStorage.getItem("tabCurTab"))) {
    curTab.value = JSON.parse(window.sessionStorage.getItem("tabCurTab"))
  } else {
    curTab.value = config.tabs.list[0] ? navList[config.tabs.list[0].key] : '';
  }


}
const onClickTab = (tab) => {
  curTab.value = navList[config.tabs.list[tab.name].key];

};

const dininghallChange = (e) => {
  // 当用户手动切换餐厅时，将新选中的餐厅 id 保存到本地存储
  localStorage.setItem("dininghall", e);
// initStatus.value = false
// proxy
//   .$post(config.curl.dininghallChange, {
//     id: e,
//   })
//   .then((res) => {
//     if (!res.errcode) {
//       res = res.result;
//       let dininghall = option.find((el) => el.value == e);
//       if(dininghall){
//         app.loginData.dininghall_title = dininghall.text;
//         app.loginData.dininghall_id = e;
//       }
//       app.loginData.token = res.token;
//       app.loginData.permission = res.permission;
//       initStatus.value = true
//     } else {
//       throw res.errmsg;
//     }
//   })
//   .catch((err) => {
//     console.log(err);
//     showToast(err);
//   })
//   .finally(() => { });
};


const getDininghall = () => {
  initStatus.value = true
  handlePremission(set_permission(router_list));
  proxy
    .$get(config.curl.ls, config.postData)
    .then((res) => {
      if (res.code === 200) {
        res = res.data;
        res.forEach((element) => {
          element.text = element.title;
          element.value = element.id;
        });
        option = res;

        // 自动选择第一个餐厅作为默认选中项
        if (res.length > 0) {
          curDininghall.value = parseInt(localStorage.getItem("dininghall"));
          // // 将选中餐厅的 id 保存到本地存储
          // localStorage.setItem("dininghall", res[0].id);
          // console.log('数据类型', typeof res[0].id);
        }
        // dininghallChange(app.loginData.dininghall_id)
      } else {
        throw res.msg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    })
    .finally(() => { });
};
getDininghall();
</script>
<style lang="scss">
.wrapper-app {
  width: 100%;
  padding-bottom: 80px;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #F7F7F7;

  .top-block {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding-left: 8px;

    .van-dropdown-menu {
      flex: 1;

      .van-dropdown-menu__bar {
        box-shadow: none !important;

        .van-dropdown-menu__item {
          justify-content: flex-start;
        }
      }
    }

    .van-icon {
      width: 50px;
      text-align: center;
    }
  }

  .tabs-block {
    padding: 16px 11px;
    margin: 16px;
    border-radius: 8px;
    background: #fff;
  }

  .nav-block {
    display: flex;
    flex-wrap: wrap;
    padding: 16px;
    margin: 16px 0; // 黄色背景只有上下间距，没有左右间距
    border-radius: 8px;
    background: #fff;
    color: #171A1D;
    font-size: 12px;
    line-height: 22px;

    .nav-item {
      width: calc((100% - 48px) / 4); // 4列布局，减去间距
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      margin-right: 16px;
      margin-bottom: 16px;

      .icon-wrap {
        width: 46px;
        height: 46px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        border-radius: 8px;
      }

      // 文字部分样式，确保居中且支持长文本
      >div:last-child {
        width: 100%;
        text-align: center;
        word-wrap: break-word;
        word-break: break-all;
        line-height: 1.2;
      }
    }

    .nav-item:nth-of-type(4n) {
      margin-right: 0;
    }

    // 最后一行没有下边距
    .nav-item:nth-last-child(-n+4) {
      margin-bottom: 0;
    }
  }

  .tab-nav-block {
    padding: 0;
    // margin-top: 16px;
  }

  .tabs-block-block {
    padding: 0;
    overflow: hidden;
  }

  .van-tabs {
    margin: 0px 0px;

    .van-tab {
      padding: 0px 16px;

      &.van-tab:first-child {
        padding-left: 8px;
      }

      &.van-tab:last-child {
        padding-right: 0px;
      }
    }
  }
}
</style>
