<template>
  <div class="device-management-container">
    <!-- 页面标题 -->
    <!-- <van-nav-bar title="设备管理" left-arrow @click-left="$router.go(-1)" /> -->

    <!-- 设备统计信息 -->
    <div class="device-stats-container">
      <div class="device-stat-item" :class="{ active: activeDeviceType === 1 }" @click="switchDeviceType(1)">
        <!-- <span class="stat-label">消费机 ({{ getTotalDeviceCount(0) }})</span> -->
        <span class="stat-label">消费机 ({{ xiaofeijiTotal }})</span>
      </div>
      <div class="device-stat-item" :class="{ active: activeDeviceType === 0 }" @click="switchDeviceType(0)">
        <!-- <span class="stat-label">收银机 ({{ getTotalDeviceCount(1) }})</span> -->
        <span class="stat-label">收银机 ({{ shouyijiTotal }})</span>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <div class="action-item" @click="showAddDeviceOptions">
        <img src="/svg/plsSign1.svg" alt="添加设备" class="action-icon" />
        <span>添加设备</span>
        <van-icon name="arrow" />
      </div>
      <div class="action-item" @click="goToCreateGroup">
        <img src="/svg/plusSign.svg" alt="创建组" class="action-icon" />
        <span>创建组</span>
        <van-icon name="arrow" />
      </div>
    </div>

    <!-- 筛选和设备组列表 -->
    <div class="content-card">
      <!-- 排序选择 -->
      <div class="sort-section">
        <div class="sort-item" @click="showSortOptions">
          <span>{{ sortName }} <img src="../../../../../public/svg/sanjiao.svg" alt="排序" class="sort-icon" /></span>
        </div>
      </div>

      <!-- 设备组列表 -->
      <div class="device-groups">
        <!-- 消费机组 -->
        <div v-if="activeDeviceType === 0">
          <div v-for="group in consumerGroups" :key="group.id" class="group-item" @click="handleItemClick(group)">
            <div class="group-icon">
              <van-image :src="group.icon" width="48" height="48" />
            </div>
            <div class="group-info">
              <div class="group-name">{{ group.title }}</div>
              <div class="group-count" v-if="group.ctype == 'group'">{{ group.count }}台</div>
              <div class="device-sn" v-else>SN: {{ group.deviceSn }}</div>
            </div>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
        </div>

        <!-- 收银机组 -->
        <div v-if="activeDeviceType === 1">
          <div v-for="group in consumerGroups" :key="group.id" class="group-item" @click="handleItemClick(group)">
            <div class="group-icon">
              <van-image :src="group.icon" width="48" height="48" />
            </div>
            <div class="group-info">
              <div class="group-name">{{ group.title }}</div>
              <div class="group-count" v-if="group.ctype == 'group'">{{ group.count }}台</div>
              <div class="device-sn" v-else>SN: {{ group.deviceSn }}</div>
            </div>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
        </div>
      </div>
    </div>

    <!-- 添加设备选项弹窗 -->
    <van-action-sheet v-model:show="showAddOptions" :actions="addActions" @select="onAddActionSelect" cancel-text="取消"
      title="添加设备" />

    <!-- 排序选项弹窗 -->
    <van-action-sheet v-model:show="showSortSheet" :actions="sortActions" @select="onSortSelect" cancel-text="取消"
      title="排序规则" />
  </div>
</template>

<script setup>
import { showToast } from 'vant';
import { ref, getCurrentInstance } from 'vue'
import * as dd from "dingtalk-jsapi";
import { useRouter } from 'vue-router'
const { proxy } = getCurrentInstance();
let configSet = ref({
  type: 1,
  sort: 0,
  get_device: 1
})
// 路由实例
const router = useRouter()
let xiaofeijiTotal = ref(0)
let shouyijiTotal = ref(0)
function getList() {
  proxy.$get("/device/get_all", configSet.value).then((res) => {
    if (res.code == 200) {
      console.log(res, 9999)
      consumerGroups.value = res.data.result
      xiaofeijiTotal.value = res.data.consumption
      shouyijiTotal.value = res.data.cashier
      // consumerGroups.value=res.data
      // console.log(res.result.data)
    } else {
      showToast(res.msg || '获取设备详情失败，请稍后重试1')
    }
  })
}
getList()
// 当前设备类型
const activeDeviceType = ref(1) // 0: 收银机, 1: 消费机

// 弹窗控制
const showAddOptions = ref(false)
const showSortSheet = ref(false)
//获取所有设备

// 消费机设备组列表
const consumerGroups = ref(
  //   [
  //   {
  //     id: 1,
  //     name: 'D3组',
  //     deviceCount: 3,
  //     icon: '/images/device-group-icon.png',
  //     devices: [
  //       {
  //         id: 1,
  //         name: '设备名称',
  //         sn: '1234567851',
  //         model: 'D3-KN1661',
  //         avatar: '/images/consumer-device.png',
  //         status: 'online'
  //       },
  //       {
  //         id: 2,
  //         name: '设备名称',
  //         sn: 'CM001234567891',
  //         model: 'D3-KN1661',
  //         avatar: '/images/consumer-device.png',
  //         status: 'offline'
  //       },
  //       {
  //         id: 3,
  //         name: '设备名称',
  //         sn: 'CM001234567892',
  //         model: 'D3-KN1661',
  //         avatar: '/images/consumer-device.png',
  //         status: 'online'
  //       }
  //     ]
  //   },
  //   {
  //     id: 2,
  //     name: '会议门牌',
  //     deviceCount: 7,
  //     icon: '/images/device-group-icon.png',
  //     devices: []
  //   },
  //   {
  //     id: 3,
  //     name: 'D3-KN0567',
  //     deviceCount: 0,
  //     icon: '/images/device-group-icon.png',
  //     devices: []
  //   },
  //   // 未分组的设备
  //   {
  //     id: 'device_4',
  //     name: 'D3-KN0567',
  //     sn: '343212122123365',
  //     deviceCount: 1,
  //     icon: '/images/consumer-device.png',
  //     isDevice: true, // 标记这是单个设备而不是组
  //     devices: []
  //   },
  //   {
  //     id: 'device_5',
  //     name: 'D3-KN0567',
  //     sn: '343212122123365',
  //     deviceCount: 1,
  //     icon: '/images/consumer-device.png',
  //     isDevice: true, // 标记这是单个设备而不是组
  //     devices: []
  //   }
  // ]
)


// 添加设备选项
const addActions = [
  {
    name: '扫一扫添加',
    value: 'scan'
  },
  {
    name: '手动添加',
    value: 'manual'
  }
]

// 排序选项
const sortActions = [
  {
    name: '按设备名称排序',
    value: 0
  },
  {
    name: '按激活时间正序',
    value: 1
  },
  {
    name: '按激活时间倒序',
    value: 2
  }
]

// 设备类型切换
const switchDeviceType = (type) => {
  activeDeviceType.value = type
  configSet.value.type = type
  getList() // 切换设备类型时重新获取设备列表
}

// 计算总设备数量
// const getTotalDeviceCount = (deviceType) => {
//   if (deviceType === 0) {
//     return consumerGroups.value.reduce((total, group) => total + group.deviceCount, 0)
//   } else {
//     return cashierGroups.value.reduce((total, group) => total + group.deviceCount, 0)
//   }
// }

// 显示添加设备选项
const showAddDeviceOptions = () => {
  showAddOptions.value = true
}
let sortName = ref("按设备名称排序")
// 显示排序选项
const showSortOptions = () => {
  showSortSheet.value = true
}

// 添加设备选项选择
const onAddActionSelect = (action) => {
  if (action.value === 'scan') {
    // 调用钉钉扫码功能
    scanToAddDevice()
  } else if (action.value === 'manual') {
    // 跳转到手动添加页面
    goToAddDevice()
  }
  showAddOptions.value = false
}

// 排序选择
const onSortSelect = (action) => {
  console.log('选择排序方式:', action)
  // 这里可以实现排序逻辑
  showSortSheet.value = false
  sortName.value = action.name
  configSet.value.sort = action.value
  getList() // 重新获取设备列表
}

// 钉钉扫码添加设备
const scanToAddDevice = () => {
  // 调用钉钉扫码API

  dd.biz.util.scan({
    type: 'qrCode', // 扫码类型，支持qr、bar，默认是qr
    onSuccess: function (result) {
      router.push({
        path: `/app/systemConfig/deviceManagement/add`,
        query: {
          type: activeDeviceType.value,
          data: result.text // 将扫码结果传递给添加设备页面
        }
      })
      console.log('1111', activeDeviceType.value)
      console.log('扫码结果:', result)
      // 处理扫码结果，添加设备
      // handleScanResult(result.text)
    },
    onFail: function (err) {
      console.error('扫码失败:', err)
      showToast('扫码失败，请重试')
    }
  })
}

// 处理扫码结果
const handleScanResult = (scanText) => {
  try {
    // 假设扫码结果是设备信息的JSON字符串
    const deviceInfo = JSON.parse(scanText)
    console.log('设备信息:', deviceInfo)
    showToast('设备添加成功')
    // 这里可以添加设备到对应的组
  } catch (error) {
    console.error('解析扫码结果失败:', error)
    showToast('无效的设备二维码')
  }
}

// 跳转到手动添加设备页面
const goToAddDevice = () => {
  router.push({
    path: `/app/systemConfig/deviceManagement/add`,
    query: {
      type: activeDeviceType.value
    }
  })
}

// 跳转到创建组页面
const goToCreateGroup = () => {
  router.push({
    path: `/app/systemConfig/deviceManagement/createGroup`,
    query: {
      type: activeDeviceType.value
    }
  })
}

// 处理项目点击（区分设备和组）
const handleItemClick = (item) => {
  console.log('点击项目:', item)
  if (item.ctype == 'device') {
    // 如果是设备，跳转到设备详情页面
    goToDeviceDetail(item.id)
  } else {
    // 如果是组，跳转到组详情页面
    goToGroupDetail(item.id)
  }
}

// 跳转到设备组详情页面
const goToGroupDetail = (groupId) => {
  console.log('跳转到设备组详情页面:', groupId)
  router.push({
    path: `/app/systemConfig/deviceManagement/groupDetail`,
    query: {
      type: activeDeviceType.value,
      groupId: groupId
    }
  })
}

// 跳转到设备详情页面
const goToDeviceDetail = (deviceId) => {
  router.push({
    path: `/app/systemConfig/deviceManagement/detail`,
    query: {
      type: activeDeviceType.value,
      id: deviceId
    }
  })
}
</script>

<style lang="scss" scoped>
.device-management-container {
  min-height: 100vh;
  background: #f7f8fa;
}

// 设备统计信息容器
.device-stats-container {
  display: flex;
  background: white;
  margin-top: -1px;

  .device-stat-item {
    flex: 1;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #969799;
    cursor: pointer;
    position: relative;

    &.active {
      color: #323233;
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background: #323233;
        border-radius: 1px;
      }
    }
  }
}

// 操作按钮区域
.action-buttons {
  display: flex;
  background: #f7f8fa;
  padding: 15px;
  padding-bottom: 14px;
  gap: 16px;
  margin-top: 8px;

  .action-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    height: 58px;

    .van-icon:first-child {
      font-size: 20px;
      color: #171A1D;
      line-height: 1;
      display: flex;
      align-items: center;
    }

    .action-icon {
      width: 26px;
      height: 26px;
      display: flex;
      align-items: center;
    }

    span {
      font-size: 16px;
      color: #171A1D;
      line-height: 1;
      display: flex;
      align-items: center;
      flex: 1;
      margin-left: 8px;
    }

    .van-icon:last-child {
      font-size: 16px;
      color: #c8c9cc;
      line-height: 1;
      display: flex;
      align-items: center;
    }

    &:active {
      background: #f7f8fa;
    }
  }
}

// 内容卡片容器
.content-card {
  background: white;
  margin: 16px;
  margin-top: 0px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

// 排序区域
.sort-section {
  padding: 16px;

  .sort-item {
    cursor: pointer;

    span {
      font-size: 14px;
      color: #323233;
      line-height: 1.4;
      display: flex;
      align-items: center;
      gap: 4px;

      .sort-icon {
        width: 20px;
        height: 20px;
        vertical-align: middle;
      }
    }
  }
}

// 设备组列表
.device-groups {
  padding: 0;

  .group-item {
    display: flex;
    align-items: center;
    padding: 16px;
    height: 80px;
    cursor: pointer;
    position: relative;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      left: 76px; // 16px padding + 48px icon + 12px margin
      right: 0;
      bottom: 0;
      height: 1px;
      background: #f7f8fa;
    }

    &:active {
      background: #f7f8fa;
    }

    .group-icon {
      margin-right: 12px;

      .van-image {
        border-radius: 8px;
      }
    }

    .group-info {
      flex: 1;

      .group-name {
        font-size: 16px;
        color: #323233;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .group-count {
        font-size: 14px;
        color: #969799;
      }

      .device-sn {
        font-size: 14px;
        color: #969799;
      }

      .device-status {
        font-size: 12px;
        color: #ff8c00;
        background: rgba(255, 140, 0, 0.1);
        padding: 2px 6px;
        border-radius: 4px;
      }
    }

    .arrow-icon {
      font-size: 16px;
      color: #c8c9cc;
    }
  }
}

// 添加设备弹窗样式
:deep(.van-action-sheet) {
  .van-action-sheet__header {
    .van-action-sheet__title {
      font-size: 14px;
      color: rgba(23, 26, 29, 0.4); // #171A1D 透明度40%
      font-weight: normal;
    }
  }

  .van-action-sheet__item {
    font-size: 17px;
    color: #171A1D;
    font-weight: normal;
  }
}
</style>