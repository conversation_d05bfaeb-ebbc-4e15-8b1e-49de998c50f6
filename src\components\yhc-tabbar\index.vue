<template>
  <van-tabbar v-model="active" @change="onChange">
    <van-tabbar-item :icon="item.icon" v-for="(item, i) in list" :key="i">{{
      item.text
    }}</van-tabbar-item>
  </van-tabbar>
</template>
<script setup>
import { ref, watch } from "vue";
const { data } = defineProps({
  data: Array,
});
const router = useRouter();
const route = useRoute();
let active = ref(0);
// 底部导航配置 - 支持通过props.data自定义或使用默认配置
let list = data || [
  {
    icon: "home-o",          // 图标名称 (字符串) - vant图标名称，如"home-o", "user-o"
    text: "首页",            // 显示文字 (字符串) - 导航项显示的文本
    path: "/home",           // 路由路径 (字符串) - 点击时跳转的路由地址
  },
  {
    icon: "records-o",       // 图标名称 (字符串) - 订单相关图标
    text: "订单",            // 显示文字 (字符串) - 导航项文本
    path: "/bill",           // 路由路径 (字符串) - 订单页面路径
  },
  {
    icon: "records-o",       // 图标名称 (字符串) - 订单相关图标
    text: "统计",            // 显示文字 (字符串) - 导航项文本
    path: "/statistics",           // 路由路径 (字符串) - 订单页面路径
  },
  {
    icon: "setting-o",       // 图标名称 (字符串) - 设置相关图标
    text: "工作台",          // 显示文字 (字符串) - 导航项文本
    path: "/app",            // 路由路径 (字符串) - 工作台页面路径
  },
  {
    icon: "user-o",          // 图标名称 (字符串) - 用户相关图标
    text: "我的",            // 显示文字 (字符串) - 导航项文本
    path: "/user",           // 路由路径 (字符串) - 个人中心页面路径
  },
];
const onChange = (i) => {
  router.push(list[i].path);
  // 清空判断产品授权是否再次展示
  // sessionStorage.clear()
};
watch(
  () => route.path,
  () => {
    let index = list.findIndex((el) => el.path === route.path);
    active.value = index != -1 ? index : 0;
  }, { immediate: true }
);
</script>
<style lang="scss" scoped></style>
