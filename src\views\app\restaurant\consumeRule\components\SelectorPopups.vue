<template>
  <div class="selector-popups">
    <!-- 选餐模式单选弹窗 -->
    <van-popup 
      v-model:show="uiState.popups.diningMode" 
      position="bottom" 
      :style="{ height: '50vh' }"
      round
      :closeable="false"
    >
      <div class="dining-mode-selector">
        <div class="top-block single-title">
          <span></span>
          <span class="title-text">选餐模式</span>
          <van-icon
            @click="uiState.temp.diningMode = formData.temporary; uiState.popups.diningMode = false"
            name="cross"
            size="18"
            color="#969799"
          />
          
        </div>
        
        <div class="list-content">
          <div class="single-select-list">
            <div
              v-for="(item, i) in OPTIONS.diningMode"
              :key="item.value"
              class="single-select-item"
              @click="onDiningModeSelect(item)"
            >
              <span class="item-text">{{ item.text }}</span>
              <van-icon
                v-if="uiState.temp.diningMode === item.value"
                name="success"
                size="18"
                color="#007fff"
              />
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 预定截止时间单选弹窗 -->
    <van-popup 
      v-model:show="uiState.popups.bookingDeadline" 
      position="bottom" 
      :style="{ height: '50vh' }"
      round
      :closeable="false"
    >
      <div class="dining-mode-selector">
        <div class="top-block single-title">
          <span></span>
          <span class="title-text">预定截止时间</span>
          <van-icon
            @click="uiState.temp.bookingDeadline = formData.booking_deadline_type; uiState.popups.bookingDeadline = false"
            name="cross"
            size="18"
            color="#969799"
          />
        </div>
        
        <div class="list-content">
          <div class="single-select-list">
            <div
              v-for="(item, i) in OPTIONS.bookingDeadline"
              :key="item.value"
              class="single-select-item"
              @click="onBookingDeadlineSelect(item)"
            >
              <span class="item-text">{{ item.text }}</span>
              <van-icon
                v-if="uiState.temp.bookingDeadline === item.value"
                name="success"
                size="18"
                color="#007fff"
              />
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 取消预定截止时间单选弹窗 -->
    <van-popup 
      v-model:show="uiState.popups.cancelBookingDeadline" 
      position="bottom" 
      :style="{ height: '50vh' }"
      round
      :closeable="false"
    >
      <div class="dining-mode-selector">
        <div class="top-block single-title">
           <span></span>
          <span class="title-text">取消预定截止时间</span>
          <van-icon
            @click="uiState.temp.cancelBookingDeadline = formData.cancel_booking_deadline_type; uiState.popups.cancelBookingDeadline = false"
            name="cross"
            size="18"
            color="#969799"
          />
        </div>
        
        <div class="list-content">
          <div class="single-select-list">
            <div
              v-for="(item, i) in OPTIONS.cancelBookingDeadline"
              :key="item.value"
              class="single-select-item"
              @click="onCancelBookingDeadlineSelect(item)"
            >
              <span class="item-text">{{ item.text }}</span>
              <van-icon
                v-if="uiState.temp.cancelBookingDeadline === item.value"
                name="success"
                size="18"
                color="#007fff"
              />
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 扣费方式单选弹窗 -->
    <van-popup 
      v-model:show="uiState.popups.chargeMethod" 
      position="bottom" 
      :style="{ height: '50vh' }"
      round
      :closeable="false"
    >
      <div class="dining-mode-selector">
        <div class="top-block single-title">
          
           <span></span>
          <span class="title-text">扣费方式</span>
          <van-icon
            @click="uiState.temp.chargeMethod = formData.charge_method; uiState.popups.chargeMethod = false"
            name="cross"
            size="18"
            color="#969799"
          />
        </div>
        
        <div class="list-content">
          <div class="single-select-list">
            <div
              v-for="(item, i) in OPTIONS.chargeMethod"
              :key="item.value"
              class="single-select-item"
              @click="onChargeMethodSelect(item)"
            >
              <span class="item-text">{{ item.text }}</span>
              <van-icon
                v-if="uiState.temp.chargeMethod === item.value"
                name="success"
                size="18"
                color="#007fff"
              />
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 时间选择器弹窗 -->
    <van-popup
      v-model:show="uiState.popups.timePicker"
      position="bottom"
      round
      :closeable="false"
    >
      <div class="time-picker-container">
        <div class="top-block">
          <span @click="cancelTimeSelection" class="cancel-btn">取消</span>
          <span class="title-text">{{ uiState.timePicker.title }}</span>
          <span @click="confirmTimeSelection" class="confirm-btn">确认</span>
        </div>
        
        <div class="time-picker-content">
          <van-time-picker
            v-model="uiState.timePicker.selectedTime"
            title=""
            :columns-type="['hour', 'minute', 'second']"
          />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  uiState: {
    type: Object,
    required: true
  },
  formData: {
    type: Object,
    required: true
  },
  OPTIONS: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits([
  'diningModeSelect',
  'bookingDeadlineSelect', 
  'cancelBookingDeadlineSelect',
  'chargeMethodSelect',
  'timeSelectionConfirm',
  'timeSelectionCancel'
])

// 选择器方法
const onDiningModeSelect = (item) => {
  emit('diningModeSelect', item)
}

const onBookingDeadlineSelect = (item) => {
  emit('bookingDeadlineSelect', item)
}

const onCancelBookingDeadlineSelect = (item) => {
  emit('cancelBookingDeadlineSelect', item)
}

const onChargeMethodSelect = (item) => {
  emit('chargeMethodSelect', item)
}

const confirmTimeSelection = () => {
  emit('timeSelectionConfirm')
}

const cancelTimeSelection = () => {
  emit('timeSelectionCancel')
}
</script>

<style lang="scss" scoped>
@use './popup-styles.scss' as *;
</style>
