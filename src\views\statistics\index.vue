<template>
  <div class="wrapper-app" v-if="initStatus">
    <!-- 顶部标签页 -->
    <div class="" v-if="curTab">
      <van-tabs ref="tabs" v-model:active="tabsActive" line-width="16px" color="#000000" :sticky="config.tabs.sticky"
         border="true" :swipeable="config.tabs.swipeable" @click-tab="onClickTab">
        <van-tab v-for="(tab, i) in config.tabs.list" :title="tab.text" :key="i">
        </van-tab>
      </van-tabs>
    </div>

    <!-- 内容区域 -->
    <div class="tabs-block tabs-block-block">
      <div v-if="curTab && curTab.length">
        <div v-for="(el, i) in curTab" :key="i + el.title">
          <div v-if="el.list.length" style="
              font-size: 14px;
              color: #969799;
              margin: 16px 0 0 16px;
            ">
            {{ el.title }}
          </div>
          <div class="nav-block tab-nav-block" v-if="el.list.length">
            <div class="nav-item van-haptics-feedback" v-for="(item, index) in el.list" :key="index + item.text"
              @click="click(item)">
              <div class="icon-wrap">
                <img class="icons" :src="`/icons/${item.icon}.png`" alt=""
                  :style="{ width: '100%', height: '100%' }" />
              </div>
              <div v-html="formatText(item.text)"></div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <van-empty image-size="80" description="没有模块啦~" />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from "vue";
import { showToast } from "vant";
let initStatus = ref(false);
const router = useRouter();
let config = {

  tabs: {
    sticky: false,
    swipeable: false,
    list: [
      {
        text: "统计",
        key: "statistics",
      },

      {
        text: "报表",
        key: "reports",
      },
    ],
  },
};

let navList = {
  show: [
    {
      title: "",
      list: [

      ],
    },
  ],
  statistics: [
    {
      title: "餐厅统计",
      list: [
        {
          text: "包间预定记录",
          url: "/",
          icon: "datav",
        },
        {
          text: "每日统计",
          url: "/",
          icon: "datav",
        },
        {
          text: "菜品销量",
          url: "/",
          icon: "datav",
        },
        {
          text: "部门就餐情况",
          url: "/",
          icon: "datav",
        },
        {
          text: "公费预定记录",
          url: "/",
          icon: "datav",
        },
        {
          text: "分类统计",
          url: "/",
          icon: "datav",
        },
      ],
    },
    {
      title: "数据统计",
      list: [
        {
          text: "人次统计",
          url: "/personCount",
          icon: "foodRelease",
        },
        {
          text: "收入统计",
          url: "/stall",
          icon: "stall",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "充值统计",
          url: "/menuTime",
          icon: "mealTime",
          color: "rgba(255,140,0, 1.0)",
        },
        {
          text: "资金统计",
          url: "/menuTimeGroup",
          icon: "mealTimeGroup",
          color: "rgba(255,140,0, 1.0)",
        },
        
      ],
    },
  ],
  reports: [
    {
      title: "餐厅报表",
      list: [
        {
          text: "订单记录",
          url: "/orm",
          icon: "",
        },
        {
          text: "菜品销量",
          url: "/",
          icon: "datav",
        },
        {
          text: "档口统计",
          url: "/",
          icon: "datav",
        },
        {
          text: "餐时统计",
          url: "/",
          icon: "datav",
        },
      ],
    },
    {
      title: "资金报表",
      list: [
        {
          text: "交易流水",
          url: "/",
          icon: "datav",
        },
        {
          text: "充值记录",
          url: "/",
          icon: "datav",
        },
        {
          text: "历史余额",
          url: "/",
          icon: "datav",
        },
      ],
    },
    {
      title: "人员报表",
      list: [
        {
          text: "账户",
          url: "/",
          icon: "datav",
        },
      ],
    },
  ],
};

const tabsActive = ref(0);
const curTab = ref({});

onMounted(() => {
  // 设置页面初始化状态
  initStatus.value = true;
  // 初始化显示统计页面
  curTab.value = navList[config.tabs.list[0].key];
});
const click = (item) => {
  if (!item.url) {
    showToast(`${item.text}未设置路径`);
    return;
  }
  window.sessionStorage.setItem("tabNum", tabsActive.value);
  window.sessionStorage.setItem("tabCurTab", JSON.stringify(curTab.value));



  router.push(item.url);
};

// 文本格式化函数：超过4个字就换行
const formatText = (text) => {
  if (text.length > 4) {
    return text.substring(0, 4) + '<br>' + text.substring(4);
  }
  return text;
};

const onClickTab = (tab) => {
  curTab.value = navList[config.tabs.list[tab.name].key];
};
</script>
<style lang="scss" scoped>
.wrapper-app {
  width: 100%;
  padding-bottom: 80px;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: #F7F7F7;

  .top-block {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding-left: 8px;

    .van-dropdown-menu {
      flex: 1;

      .van-dropdown-menu__bar {
        box-shadow: none !important;

        .van-dropdown-menu__item {
          justify-content: flex-start;
        }
      }
    }

    .van-icon {
      width: 50px;
      text-align: center;
    }
  }

  .tabs-block {
    padding: 16px 11px;
    margin: 16px;
    border-radius: 8px;
    background: #fff;
  }

  .nav-block {
    display: flex;
    flex-wrap: wrap;
    padding: 16px;
    margin: 16px 0; // 黄色背景只有上下间距，没有左右间距
    border-radius: 8px;
    background: #fff;
    color: #171A1D;
    font-size: 12px;
    line-height: 22px;

    .nav-item {
      width: calc((100% - 48px) / 4); // 4列布局，减去间距
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      margin-right: 16px;
      margin-bottom: 16px;

      .icon-wrap {
        width: 46px;
        height: 46px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        border-radius: 8px;
      }

      // 文字部分样式，确保居中且支持长文本
      >div:last-child {
        width: 100%;
        text-align: center;
        line-height: 1.3;
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        height: auto;
        max-height: 2.6em; // 限制最多显示2行
      }
    }

    .nav-item:nth-of-type(4n) {
      margin-right: 0;
    }

    // 最后一行没有下边距
    .nav-item:nth-last-child(-n+4) {
      margin-bottom: 0;
    }
  }

  .tab-nav-block {
    padding: 0;
    // margin-top: 16px;
  }

  .tabs-block-block {
    padding: 0;
    overflow: hidden;
  }

  .van-tabs {
    margin: 0px 0px;

    .van-tab {
      padding: 0px 16px;

      &.van-tab:first-child {
        padding-left: 8px;
      }

      &.van-tab:last-child {
        padding-right: 0px;
      }
    }
  }
}
</style>
