<template>
  <div class="skeleton-demo-container">
    <!-- 列表组件演示 -->
    <yhc-list :key="listConfig.key || 'default'" :config="listConfig" @onButClick="onAddClick" ref="listRef">
      <!-- 新增按钮插槽 -->
      <template #header>
        <div class="add-button-container">
          <div class="add-button" @click="onAddClick">
            <img src="/img/add.svg" alt="新增" class="add-icon" />
            <span>新增档口</span>
          </div>
        </div>
      </template>

      <template #default="{ item, index }">
        <div class="demo-item" @click.stop="onCardClick(item)">
          <div class="item-content">
            <div class="item-title">{{ item.title }}</div>
            <!-- "device_consumptions": "[{"id": 10, "title": "FT1-MINI-0465"}]", 
        "device_cashiers": "{"id": 4, "title": "模拟器"}",  -->
        <!-- JSON.parse(...).map is not a function" -->
            <div class="item-desc">消费机：{{ item.device_consumptions ? consumption(item.device_consumptions) : '暂无' }}</div>
            <div class="item-time">收银机：{{ item.device_cashiers ?  cashier(item.device_cashiers) : '暂无' }}</div>
          </div>
        </div>
      </template>
    </yhc-list>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { showToast } from 'vant'
import { getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance();
const router = useRouter();

// 骨架屏配置
const skeletonConfig = reactive({
  isShow: false,
  count: 3,
  row: 2,
  rowWidth: ['100%', '60%', '80%'],
  avatar: true,
  avatarSize: '40px',
  avatarShape: 'round',
  title: true,
  titleWidth: '50%',
  duration: 500
})
// 列表组件引用
const listRef = ref(null)
// 列表配置
const listConfig = reactive({
  curl: {
    ls: '/stall/get_ls' // 留空，使用模拟数据
  },
  postData: {},
  search: {
    isShow: true,
    isShowPopup: false
  },
  tabs: {
    isShow: false
  },
  button: {
    isShow: false,
  },
  skeleton: skeletonConfig,
  // 模拟数据格式化
  format: (data) => {
    // 这里可以对数据进行格式化处理
    console.log('格式化数据:', data)
  },
  // 添加模拟数据标识
  mockData: false
})

const consumption = (item) => {
  return JSON.parse(item).map(i => i.title).join('；')
}
const cashier = (item) => {
  // device_cashiers": "{"id": 4, "title": "模拟器"}"
  return JSON.parse(item).title
}
// 新增按钮点击事件
const onAddClick = () => {
  router.push('/stallAdd')
}
const onCardClick = (item) => {
  router.push({ path: "/stallDetail", query: { id: item.id } });
};
const setRight = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title:'档口管理',
  });
};
setRight()

</script>

<style lang="scss" scoped>
.skeleton-demo-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.add-button-container {
  padding: 16px;
  padding-bottom: 0;
  .add-button {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    .add-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: normal;
      line-height: 22px;
      color: #323233;
    }
  }
}

.demo-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin:16px;
  background: #fff;
  border-radius: 8px;
  .item-content {
    flex: 1;
    min-width: 0; /* 确保flex子项能够收缩 */
    .item-title {
      font-size: 16px;
font-weight: 500;
line-height: 22px;
letter-spacing: normal;
color: #171A1D;
margin-bottom: 8px;
    }

    .item-desc {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
font-weight: normal;
line-height: 20px;
letter-spacing: normal;
color: #9E9E9E;
    }

    .item-time {
      font-size: 14px;
font-weight: normal;
line-height: 20px;
letter-spacing: normal;
color: #9E9E9E;
    }
  }

  .item-action {
    margin-left: 12px;
  }
}
</style>
