<template>
  <div class="calendar">
    <div class="header">
      <div class="title">{{ formatTitle }}</div>
      <!-- <van-divider /> -->
    </div>
    <div class="weekdays">
      <div v-for="day in daysOfWeek" :key="day" class="day">{{ day }}</div>
    </div>
    <template v-if="isShowLong">
      <div
        class="days"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        :class="isShowLong ? 'appearAnimation' : ''"
      >
        <div
          v-for="day in days"
          :key="day.date"
          class="day"
          @click="select(day)"
        >
          <div
            class="text"
            :class="{
              today: isToday(day),
              selected: isSelected(day),
              notCurrentMonth: isNotCurrentMonth(day),
            }"
          >
            {{ day.day }}
          </div>
          <div class="tips" :style="{ color: day.haveText ? '' : '#fff' }" v-if="day.haveText">
            {{ day.haveText ? "已发布" : " " }}
          </div>
          <div v-else style="height: 20px;">
            <!-- 占位空间，保持布局一致 -->
          </div>
        </div>
        <div class="bgc">{{ animatedText }}</div>
      </div>
    </template>
    <template v-else>
      <div class="days" :class="isShowLong ? '' : 'vanishAnimation'">
        <div
          v-for="day in weeksdays"
          :key="day.date"
          class="day"
          @click="select(day)"
        >
          <div
            class="text"
            :class="{
              today: isToday(day),
              selected: isSelected(day),
              notCurrentMonth: isNotCurrentMonth(day),
            }"
          >
            {{ day.day }}
          </div>
          <div class="tips" :style="{ color: day.haveText ? '' : '#fff' }" v-if="day.haveText">
            {{ day.haveText ? "已发布" : " " }}
          </div>
          <div v-else  style="height: 20px;">
            <!-- <div>w无</div> -->
          </div>
        </div>
      </div>
    </template>
    <!-- 装饰线 -->
    <div class="decoration">
      <!-- <van-divider :style="{ padding: '0 16px' }"> -->
        <van-icon
          @click="switchShow"
          name="arrow-down"
          class="fs20"
          :class="isShowLong ? 'rotate180' : ''"
        />
      <!-- </van-divider> -->
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  computed,
} from "vue";
import dayjs from "dayjs";
// import "animate.css";

const props = defineProps({
  selectedDate: {
    type: Date,
    default: new Date(),
  },
  dateList: {
    type: Array,
  },
});
const emit = defineEmits(["update:selectedDate", "update:dateList"]);

const weekdays = ["日", "一", "二", "三", "四", "五", "六"];
const currentDate = ref(new Date());
const selectedDate = ref(props.selectedDate || currentDate.value);

const daysOfWeek = computed(() => {
  return weekdays;
});
const days = computed(() => {
  const year = currentDate.value.getFullYear();
  const month = currentDate.value.getMonth();
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  const daysInLastMonth = new Date(year, month, 0).getDate();
  const firstDayOfMonth = new Date(year, month, 1).getDay();

  const days = [];
  let day = 1;
  let lastMonthDay = daysInLastMonth - firstDayOfMonth + 1;
  let nextMonthDay = 1;

  for (let i = 0; i < 6 * 7; i++) {
    if (i < firstDayOfMonth) {
      days.push({
        date: new Date(year, month - 1, lastMonthDay),
        day: lastMonthDay,
        isLastMonth: true,
        isNextMonth: false,
      });
      lastMonthDay++;
    } else if (i >= firstDayOfMonth + daysInMonth) {
      days.push({
        date: new Date(year, month + 1, nextMonthDay),
        day: nextMonthDay,
        isLastMonth: false,
        isNextMonth: true,
      });
      nextMonthDay++;
    } else {
      const date = new Date(year, month, day);
      // console.log("当月日期 --->", day);
      let temtext = {
        date,
        day,
        isLastMonth: false,
        isNextMonth: false,
      };
      if (props.dateList) {
        // 构建当前日期字符串格式 YYYY-MM-DD (注意：month需要+1，因为getMonth()返回0-11)
        const currentDateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        // 检查当前日期是否在已发布日期列表中
        temtext.haveText = props.dateList.includes(currentDateStr);
        // 调试信息
        // console.log(`月视图调试 - 日期: ${currentDateStr}, 发布状态: ${temtext.haveText}, dateList:`, props.dateList);
      }
      days.push(temtext);
      day++;
    }
  }
  return days;
});

const weeksdays = computed(() => {
  // const baseDate = dayjs("2023-03-05");
  // const baseDate = dayjs(new Date());
  // console.log("查看时间格式组件", selectedDate.value);
  const baseDate = selectedDate.value ? dayjs(selectedDate.value) : dayjs();

  // 初始化周日日期为固定日期的当周周日
  let sunday = baseDate.startOf("week");

  // 周日到周六日期数组
  const dates = [];

  // 获取当前日期天数
  const nowdate = new Date();
  const nowmonth = nowdate.getMonth() + 1;

  for (let i = 0; i < 7; i++) {
    const temdate = sunday.add(i, "day");
    const date = new Date(temdate.year(), temdate.month(), temdate.date());
    const day = temdate.date();
    const month = date.getMonth() + 1;
    // console.log("查看周日期 --->", date, day, month);
    if (!props.dateList) {
      dates.push({
        date,
        day,
        isLastMonth: nowmonth > month ? true : false,
        isNextMonth: nowmonth < month ? true : false,
      });
    } else {
      let text = {
        date,
        day,
        isLastMonth: nowmonth > month ? true : false,
        isNextMonth: nowmonth < month ? true : false,
      };
      // 构建当前日期字符串格式 YYYY-MM-DD，不论是否跨月都检查发布状态
      const currentDateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      // 检查当前日期是否在已发布日期列表中
      text.haveText = props.dateList.includes(currentDateStr);
      // 调试信息
      // console.log(`周视图调试 - 日期: ${currentDateStr}, 发布状态: ${text.haveText}, dateList:`, props.dateList);
      dates.push(text);
    }
  }

  return dates;
});

const title = computed(
  () =>
    `${selectedDate.value.getFullYear()}-${
      selectedDate.value.getMonth() + 1
    }-${selectedDate.value.getDate()}`
);
// 格式化标题显示，参考设计图样式
const formatTitle = computed(() => {
  const date = selectedDate.value;
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  // 获取星期几
  const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
  const weekDay = weekDays[date.getDay()];

  return `${year}年${month}月${day}日 周${weekDay}`;
});
const prevMonth = () => {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() - 1,
    1
  );
  selectedDate.value = currentDate.value;
  emit("update:selectedDate", selectedDate.value);
  emit("update:dateList", undefined);
};

const nextMonth = () => {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() + 1,
    1
  );
  selectedDate.value = currentDate.value;
  emit("update:selectedDate", selectedDate.value);
  emit("update:dateList", undefined);
};

const isToday = (day) => {
  const today = new Date();
  return day.date.toDateString() === today.toDateString();
};

import { dishesInfo } from "@/store/dishes_public";
let dishes = dishesInfo();
const isSelected = (day) => {
  if (dishes.Datatime) {
    let str = dishes.selectedDate.replace(/\-/g, "/");
    selectedDate.value = dayjs(str).toDate();
    return day.date.toDateString() === selectedDate.value.toDateString();
  } else {
    return day.date.toDateString() === selectedDate.value.toDateString();
  }
  // if (day.date.toDateString() === selectedDate.value.toDateString()) {
  //   console.log(
  //     "查看选择 --->",
  //     day.date.toDateString(),
  //     selectedDate.value.toDateString()
  //   );
  // }
};

const isNotCurrentMonth = (day) => {
  return day.isLastMonth || day.isNextMonth;
};

const select = (day) => {
  dishes.Datatime = null;
  // console.log("查看选择", day);

  // 如果点击的是其他月份的日期，先切换到对应月份
  if (day.isLastMonth) {
    // 点击上个月日期，切换到上个月
    currentDate.value = new Date(
      currentDate.value.getFullYear(),
      currentDate.value.getMonth() - 1,
      1
    );
  } else if (day.isNextMonth) {
    // 点击下个月日期，切换到下个月
    currentDate.value = new Date(
      currentDate.value.getFullYear(),
      currentDate.value.getMonth() + 1,
      1
    );
  }

  // 选中点击的日期
  selectedDate.value = day.date;
  emit("update:selectedDate", day.date);
  emit("update:dateList", undefined);
};

// 屏幕滑动
const startX = ref(0);
const currentX = ref(0);
const isSliding = ref(false);

const handleTouchStart = (event) => {
  if (isSliding.value) return;

  startX.value = event.touches[0].clientX;
  isSliding.value = true;
};

const handleTouchMove = (event) => {
  if (!isSliding.value) return;
  currentX.value = event.touches[0].clientX;
  const diffX = currentX.value - startX.value;

  if (diffX > 0) {
    // 向右滑动
    console.log("测试滑动向右 --->", "上个月");
    prevMonth();
  } else {
    // 向左滑动
    console.log("测试滑动向左 --->", "下个月");
    nextMonth();
  }

  isSliding.value = false;
};

// 日历背景图
const animatedText = computed(() => {
  return currentDate.value.getMonth() + 1 > 9
    ? currentDate.value.getMonth() + 1
    : `0${currentDate.value.getMonth() + 1}`;
});

// 展示内容切换
let isShowLong = ref(false);

const switchShow = () => {
  isShowLong.value = !isShowLong.value;
  let str;
  if (dishes.selectedDate) {
    str = dishes.selectedDate.replace(/\-/g, "/");
  }
  selectedDate.value = str ? dayjs(str).toDate() : new Date();
};
</script>

<style scoped lang="scss">
.calendar {
    // width: 343px;
    // height: 174px;
    // border-radius: 8px;
    // display: flex;
    // flex-direction: column;
    // justify-content: start;
    // align-items: start;
    // row-gap: 16px;
    // column-gap: 16px;
    // background: #FFFFFF;
  // margin: 2px;
  margin-top: 16px;
  border-radius: 12px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  // height: 195px;
  background-color: #fff;
  // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header {
  padding: 16px 16px 16px;
  // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  // color: #fff;

  .title {
    text-align: center;
    font-size: 14px;
    // font-weight: 600;
    margin: 0;
    white-space: nowrap;
    color: #323233;
    font-family: "PingFang SC";
    font-size: 14px;
  }

  :deep(.van-divider) {
    margin: 16px 0 0;
    // border-color: rgba(255, 255, 255, 0.2);
  }
}

.weekdays {
  display: flex;
  justify-content: space-around;
  padding: 16px 0;
  // background-color: #f8f9fa;
  // border-bottom: 1px solid #ebedf0;

  .day {
    font-size: 13px;
    font-weight: 500;
    // height: 10px;
    color: #646566;
  }
}

.days {
  position: relative;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  // padding: 0px;
  // padding-bottom: 50px;
  justify-items: center;

  .day {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 99;
    cursor: pointer;
    transition: all 0.2s ease;
    // padding-bottom: 50px;

    .text {
      width: 36px;
      height: 36px;
      line-height: 34px; /* 调整行高以适应边框 */
      border-radius: 8px; /* 改为圆角矩形 */
      text-align: center;
      font-size: 14px;
      font-weight: 500;
      color: #323233;
      transition: all 0.2s ease;
      box-sizing: border-box; /* 确保边框不影响尺寸 */

      &:hover {
        background-color: #f0f1ff;
        color: #667eea;
      }
    }

    .tips {
      margin-top: 4px;
     color: #1989FA;
      font-family: "PingFang SC";
      font-size: 10px;
      line-height: 14px;
    }
  }

  .bgc {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    font-size: 120px;
    font-weight: 900;
    color: #f5f5f5;
    z-index: 1;
    user-select: none;
    pointer-events: none;
  }
}

.appearAnimation {
  // margin-bottom: 50px;
  animation: slideInDown 0.3s ease-out;
}

.vanishAnimation {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.decoration {
  text-align: center;
  padding: 12px 0;
  background-color: #fff;
  // border-top: 1px solid #ebedf0;

  :deep(.van-divider) {
    margin: 0;

    .van-divider__content {
      // background-color: #fff;
      padding: 0 16px;
    }
  }

  .fs20 {
    font-size: 20px;
    color: #969799;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      color: #667eea;
    }
  }

  .rotate180 {
    transform: rotate(-180deg);
    color: #667eea;
  }
}

/* 当天日期样式 - 蓝色边框，蓝色文字 */
.today {
  // background: transparent !important;
  // color: #1989FA !important;
  // border: 2px solid #1989FA;
  // box-shadow: none;
  width: 45px;
    height: 44px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    row-gap: 2px;
    column-gap: 2px;
    padding: 5px 12px 5px 12px;
    background: rgba(25, 137, 250, 0.1);
    border: 1px solid #1989FA;
}

/* 选中日期样式 - 蓝色背景，白色文字 */
.selected {
  width: 45px;
    height: 44px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    row-gap: 2px;
    column-gap: 2px;
    padding: 5px 12px 5px 12px;
    background: #1989FA;
  // background: #1989FA !important;
  color: white !important;
  // border: none;
  // box-shadow: none;
}

.notCurrentMonth {
  color: #c8c9cc !important;

  .text {
    opacity: 0.5;
  }
}
</style>
