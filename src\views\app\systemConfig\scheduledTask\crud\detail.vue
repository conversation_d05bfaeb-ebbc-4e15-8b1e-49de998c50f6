<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form :config="basicFormConfig" pageType="detail" :editRedirectConfig="editRedirectConfig"
      @onSubmit="onBasicSubmit" />
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const route = useRoute()
const { proxy } = getCurrentInstance()

// 当前选中的任务类型
const currentTaskType = ref('')

// 动态获取用户标签文本的函数
const getUserLabelText = (taskType) => {
  const labelMap = {
    '3': '适用人员',  // 发放补贴
    '1': '适用人员',  // 扣除补贴
    '2': '清空人员',  // 清空待还
    '0': '预定人员',  // 餐食预定
    '4': '适用人员',  // 数据统计通知
    '5': '适用人员'   // 餐时预定通知
  }
  return labelMap[taskType] || '适用人员'
}

// 反向字段映射函数 - 将后端的 all_user 字段映射到前端对应的字段名
const mapFieldsFromBackend = (backendData) => {
  const mappedData = { ...backendData }

  // 根据任务类型确定应该映射到哪个前端字段
  const taskType = String(backendData.cron_type || '')

  // 字段映射表（反向映射）
  const reverseFieldMappings = {
    '3': 'applicable_user_grant',    // 发放补贴
    '1': 'applicable_user_deduct',   // 扣除补贴
    '2': 'clear_user',               // 清空待还
    '0': 'booking_user'              // 餐食预定
  }

  // 如果后端数据包含 all_user 字段，需要映射到对应的前端字段
  if (mappedData.hasOwnProperty('all_user') && reverseFieldMappings[taskType]) {
    const frontendKey = reverseFieldMappings[taskType]
    mappedData[frontendKey] = mappedData.all_user

    console.log(`反向字段映射: all_user → ${frontendKey}`, mappedData.all_user)
    console.log('任务类型:', taskType, '映射到字段:', frontendKey)
  }

  return mappedData
}

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    id: route.query.id // 从路由获取id参数
  },
  curl: {
    info: '/schedule_task/get_info', // 获取详情接口
    del: '/schedule_task/post_del' // 删除接口
  },
  // 数据格式化函数 - yhc-form 会在获取到后端数据后调用此函数
  format: (data) => {
    console.log('🔍 [详情页面] format 函数被调用')
    console.log('🔍 [详情页面] 原始后端数据:', JSON.stringify(data, null, 2))

    // 根据任务类型确定应该映射到哪个前端字段
    const taskType = String(data.cron_type || '')
    console.log('🔍 [详情页面] 任务类型:', taskType)

    // 字段映射表（反向映射）
    const reverseFieldMappings = {
      '3': 'applicable_user_grant',    // 发放补贴
      '1': 'applicable_user_deduct',   // 扣除补贴
      '2': 'clear_user',               // 清空待还
      '0': 'booking_user'              // 餐食预定
    }

    // 检查是否有 all_user 字段
    console.log('🔍 [详情页面] 是否包含 all_user 字段:', data.hasOwnProperty('all_user'))
    console.log('🔍 [详情页面] all_user 值:', data.all_user)

    // 如果后端数据包含 all_user 字段，需要映射到对应的前端字段
    if (data.hasOwnProperty('all_user') && reverseFieldMappings[taskType]) {
      const frontendKey = reverseFieldMappings[taskType]

      // 确保数据类型正确 - yhc-radio-group 可能需要字符串类型
      const mappedValue = String(data.all_user)
      data[frontendKey] = mappedValue

      console.log('✅ [详情页面] 反向字段映射成功:')
      console.log(`   all_user (${data.all_user}) → ${frontendKey} (${mappedValue})`)
      console.log('🔍 [详情页面] 映射后的完整数据:', JSON.stringify(data, null, 2))
    } else {
      console.log('❌ [详情页面] 字段映射失败:')
      console.log('   - 是否有 all_user:', data.hasOwnProperty('all_user'))
      console.log('   - 任务类型映射:', reverseFieldMappings[taskType])
    }

    // 注意：yhc-form 的 format 函数是直接修改 data 对象，不需要返回值
  },
  groupForm: [
    [0, 1],
    [1, 2],
    [2, 3],
    [3, 5],
    [5, 6],
  ],
  form: [
    {
      label: "任务名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入",
      required: true,
      disabled: true,
      rules: [{ required: true, message: "请填写任务名称" }],
    },
    {
      label: "任务类型",
      key: "cron_type",
      component: "yhc-picker",
      disabled: true,
      opts: {
        url: "",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: "id",
        defaultList: [
          { id: 3, title: "发放补贴" },
          { id: 1, title: "扣除补贴" },
          { id: 2, title: "清空待还" },
          { id: 0, title: "餐食预定" },
          { id: 4, title: "数据统计通知" },
          { id: 5, title: "餐食预定通知" },
        ],
      },
      child: {
        showMode: true,
        map: {
          3: [
            {
              label: "定时给用户发放补贴",
              key: "yhc-desc",
            },
            {
              label: "适用人员",
              key: "applicable_user_grant",
              component: "yhc-radio-group",
              default: "0",
              disabled: true,
              options: [
                { value: "0", label: "全部人员" },
                { value: "1", label: "指定人员" }
              ],
              shape: "dot",
              child: {
                map: {
                  "1": [
                    {
                      label: "内部员工",
                      key: "userlst",
                      component: "yhc-select-user",
                      disabled: true,
                    },
                    {
                      label: "外部员工",
                      key: "outsiderlst",
                      component: "yhc-select-user",
                      disabled: true,
                    },
                  ],
                },
                form: [],
              },
            },
            {
              label: "发放金额",
              key: "discount_amount",
              component: "yhc-input",
              type: "number",
              placeholder: "请输入",
              "right-icon": "元",
              disabled: true,
              min: 0.01,
              decimalPlaces: true
            },
            {
              label: "备注",
              key: "desc",
              component: "yhc-input",
              placeholder: "请输入",
              type: "textarea",
              rows: 3,
              disabled: true,
              maxlength: 50,
              showWordLimit: true
            }
          ],
          1: [
            {
              label: "定时扣除用户补贴",
              key: "yhc-desc",
            },
            {
              label: "适用人员",
              key: "applicable_user_deduct",
              component: "yhc-radio-group",
              default: "0",
              disabled: true,
              options: [
                { value: "0", label: "全部人员" },
                { value: "1", label: "指定人员" }
              ],
              shape: "dot",
              child: {
                map: {
                  "1": [
                    {
                      label: "内部员工",
                      key: "userlst",
                      component: "yhc-select-user",
                      disabled: true,
                    },
                    {
                      label: "外部员工",
                      key: "outsiderlst",
                      component: "yhc-select-user",
                      disabled: true,
                    },
                  ],
                },
                form: [],
              },
            },
            {
              label: "扣除类型",
              key: "deduct_type",
              default: "0",
              component: "yhc-picker",
              placeholder: "请选择",
              disabled: true,
              opts: {
                url: "",
                postData: {},
                merge: false,
                multiple: false,
                text_key: "title",
                contrast_key: "id",
                keyMap: "id",
                defaultList: [
                  { id: "0", title: "定额扣除" },
                  { id: "1", title: "全额扣除" },
                ]
              },
              child: {
                showMode: true,
                map: {
                  "0": [
                    {
                      label: "扣除金额",
                      key: "min_amount",
                      component: "yhc-input",
                      type: "number",
                      placeholder: "请输入",
                      "right-icon": "元",
                      disabled: true,
                      decimalPlaces: true
                    },
                  ],
                },
                form: [],
              },
            },
            {
              label: "备注",
              key: "desc",
              component: "yhc-input",
              placeholder: "请输入",
              type: "textarea",
              rows: 3,
              disabled: true,
              maxlength: 50,
              showWordLimit: true
            }
          ],
          2: [
            {
              label: "定时扣除用户待还欠款",
              key: "yhc-desc",
            },
            {
              label: "清空人员",
              key: "clear_user",
              component: "yhc-radio-group",
              default: "0",
              disabled: true,
              options: [
                { value: "0", label: "全部人员" },
                { value: "1", label: "指定人员" }
              ],
              shape: "dot",
              child: {
                map: {
                  "1": [
                    {
                      label: "内部员工",
                      key: "userlst",
                      component: "yhc-select-user",
                      disabled: true,
                    },
                    {
                      label: "外部员工",
                      key: "outsiderlst",
                      component: "yhc-select-user",
                      disabled: true,
                    },
                  ],
                },
                form: [],
              },
            },
            {
              label: "备注",
              key: "desc",
              component: "yhc-input",
              placeholder: "请输入",
              type: "textarea",
              rows: 3,
              disabled: true,
              maxlength: 50,
              showWordLimit: true
            }
          ],
          0: [
            {
              label: "定时餐食预定",
              key: "yhc-desc",
            },
            {
              label: "预定人员",
              key: "booking_user",
              component: "yhc-radio-group",
              default: "0",
              disabled: true,
              options: [
                { value: "0", label: "全部人员" },
                { value: "1", label: "指定人员" }
              ],
              shape: "dot",
              child: {
                map: {
                  "1": [
                    {
                      label: "内部员工",
                      key: "userlst",
                      component: "yhc-select-user",
                      disabled: true,
                    },
                    {
                      label: "外部员工",
                      key: "outsiderlst",
                      component: "yhc-select-user",
                      disabled: true,
                    },
                    {
                      label: "餐厅",
                      key: "dininghall_id",
                      component: "yhc-picker",
                      disabled: true,
                      LinkKeyConfig: [
                        {
                          key: "repast_id",
                          postData: {
                            dininghall_id: "dininghall_id",
                          },
                        },
                        {
                          key: "window_id",
                          postData: {
                            dininghall_id: "dininghall_id",
                          },
                        },
                      ],
                      opts: {
                        url: "dininghall/get_all",
                        postData: {},
                        merge: false,
                        multiple: false,
                        text_key: "title",
                        contrast_key: "id",
                        keyMap: "id",
                        defaultList: [],
                      },
                      card: {
                        title: "title",
                      },
                      child: {
                        showMode: true,
                        form: [
                          {
                            label: "餐时",
                            key: "repast_id",
                            component: "yhc-picker",
                            disabled: true,
                            opts: {
                              url: "/mealtime/get_all",
                              postData: {},
                              merge: false,
                              multiple: false,
                              text_key: "title",
                              contrast_key: "id",
                              keyMap: "id",
                              defaultList: [],
                            },
                            card: {
                              title: "title",
                              desc: "start_time",
                            },
                          },
                          {
                            label: "档口",
                            key: "window_id",
                            component: "yhc-picker",
                            disabled: true,
                            opts: {
                              url: "/stall/get_all",
                              postData: {},
                              merge: false,
                              multiple: false,
                              text_key: "title",
                              contrast_key: "id",
                              keyMap: "id",
                              defaultList: [],
                            },
                            card: {
                              title: "title",
                            },
                          },
                        ],
                      },
                    },
                    {
                      label: "考勤规则",
                      key: "attendance_rule_id",
                      component: "yhc-picker",
                      placeholder: "请选择",
                      disabled: true,
                      card: {
                        title: "rule_name",
                      },
                      opts: {
                        url: "/attendance_rule/get_all",
                        postData: {},
                        merge: false,
                        multiple: false,
                        text_key: "rule_name",
                        contrast_key: "id",
                        keyMap: 'id',
                        defaultList: []
                      },
                    },
                  ],
                },
                form: [],
              },
            },
          ],
          4: [
            {
              label: "通知接收人",
              key: "userlst",
              component: "yhc-select-user",
              disabled: true,
            },
            {
              label: "餐厅",
              key: "dininghall_id",
              component: "yhc-picker",
              disabled: true,
              LinkKeyConfig: [
                {
                  key: "repast_id",
                  postData: {
                    dininghall_id: "dininghall_id",
                  },
                },
                {
                  key: "window_id",
                  postData: {
                    dininghall_id: "dininghall_id",
                  },
                },
              ],
              opts: {
                url: "dininghall/get_all",
                postData: {},
                merge: false,
                multiple: false,
                text_key: "title",
                contrast_key: "id",
                keyMap: "id",
                defaultList: [],
              },
              card: {
                title: "title",
              },
              child: {
                showMode: true,
                form: [
                  {
                    label: "餐时",
                    key: "repast_id",
                    component: "yhc-picker",
                    disabled: true,
                    opts: {
                      url: "/repast/get_all",
                      postData: {},
                      merge: false,
                      multiple: false,
                      text_key: "title",
                      contrast_key: "id",
                      keyMap: "id",
                      defaultList: [],
                    },
                    card: {
                      title: "title",
                      desc: "start_time",
                    },
                  },
                  {
                    label: "档口",
                    key: "window_id",
                    component: "yhc-picker",
                    disabled: true,
                    opts: {
                      url: "/window/get_all",
                      postData: {},
                      merge: false,
                      multiple: false,
                      text_key: "title",
                      contrast_key: "id",
                      keyMap: "id",
                      defaultList: [],
                    },
                    card: {
                      title: "title",
                    },
                  },
                ],
              },
            },
            {
              label: "统计数据",
              key: "data_keys",
              component: "yhc-picker",
              placeholder: "请选择",
              disabled: true,
              card: {
                title: "title",
              },
              opts: {
                url: "",
                postData: {},
                merge: false,
                multiple: true,
                text_key: "title",
                contrast_key: "id",
                keyMap: {},
                defaultList: [
                  { id: 1, title: "预定份数" },
                  { id: 2, title: "消费人数" },
                  { id: 3, title: "营收统计" },
                ]
              },
            },
          ],
          5: [
            {
              label: "通知接收人",
              key: "userlst",
              component: "yhc-select-user",
              disabled: true,
            },
            {
              label: "餐厅",
              key: "dininghall_id",
              component: "yhc-picker",
              disabled: true,
              LinkKeyConfig: [
                {
                  key: "repast_id",
                  postData: {
                    dininghall_id: "dininghall_id",
                  },
                },
                {
                  key: "window_id",
                  postData: {
                    dininghall_id: "dininghall_id",
                  },
                },
              ],
              opts: {
                url: "dininghall/get_all",
                postData: {},
                merge: false,
                multiple: false,
                text_key: "title",
                contrast_key: "id",
                keyMap: "id",
                defaultList: [],
              },
              card: {
                title: "title",
              },
              child: {
                showMode: true,
                form: [
                  {
                    label: "餐时",
                    key: "repast_id",
                    component: "yhc-picker",
                    disabled: true,
                    opts: {
                      url: "/repast/get_all",
                      postData: {},
                      merge: false,
                      multiple: false,
                      text_key: "title",
                      contrast_key: "id",
                      keyMap: "id",
                      defaultList: [],
                    },
                    card: {
                      title: "title",
                      desc: "start_time",
                    },
                  },
                  {
                    label: "档口",
                    key: "window_id",
                    component: "yhc-picker",
                    disabled: true,
                    opts: {
                      url: "/window/get_all",
                      postData: {},
                      merge: false,
                      multiple: false,
                      text_key: "title",
                      contrast_key: "id",
                      keyMap: "id",
                      defaultList: [],
                    },
                    card: {
                      title: "title",
                    },
                  },
                ],
              },
            },
          ],
        },
        form: [],
      },
    },
    {
      label: "重复",
      key: "cycle",
      component: "yhc-picker",
      placeholder: "请选择",
      disabled: true,
      card: {
        title: "title",
      },
      opts: {
        url: "",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: "id",
        defaultList: [
          { id: 1, title: "不重复" },
          { id: 2, title: "每天" },
          { id: 3, title: "每周" },
          { id: 4, title: "每月" },
        ]
      },
    },
    {
      label: '执行时间',
      key: "start_time",
      component: "yhc-picker-date",
      placeholder: "请输入",
      required: true,
      disabled: true,
      rules: [{ required: true, message: "请填写执行时间" }],
    },
    {
      label: '终止时间',
      key: "end_time",
      component: "yhc-picker-date",
      placeholder: "请输入",
      required: true,
      disabled: true,
      rules: [{ required: true, message: "请填写执行时间" }],
    },
    {
      label: "启动时间须>当前时间，终止时间须>启动时间",
      component: "yhc-desc",
    },
  ]
}

// 修改按钮跳转配置
const editRedirectConfig = {
  path: '/systemConfig/scheduledTask/add', // 跳转到新增页面进行编辑
  query: {
    id: route.query.id, // 传递id参数
    from: 'detail' // 标识来源
  }
}

const setRight = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: '计划任务详情',
  });
};
setRight()

// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>