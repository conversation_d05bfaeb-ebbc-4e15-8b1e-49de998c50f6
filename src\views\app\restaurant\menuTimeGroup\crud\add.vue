<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="add"
      @onSubmit="onBasicSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const {proxy} = getCurrentInstance();
const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    // 如果有id参数，则为编辑模式
    ...(route.query.id ? { id: route.query.id } : {})
  },
  curl: {
    add: '/mealtime_group/post_add', // 新增接口
    edit: '/mealtime_group/post_modify', // 编辑接口
    info: '/mealtime_group/get_info' // 获取详情接口（编辑时需要）
  },
  groupForm: [
    [0, 1],
    [1, 3],
    [3, 4]
  ],
  form: [
    {
      label: "餐时名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请填写用户名" }],
    },
    {
      label: "关联餐时",
      key: "repasts",
      component: "yhc-picker",
      placeholder: "请选择",
      required: true,
      rules: [{ required: true, message: "请选择关联餐时" }],
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false
      },
      card: {
        title: "title",
        slotMap: {
          desc: [
            {
              title: "开始时间：",
              key: "start_time",
            },
            {
              title: "结束时间：",
              key: "end_time",
            },
          ],
        },
      },
      opts: {
        url: "/mealtime/get_all",
        postData: {},
        merge: false,
        multiple: true,
        maxlength:14,
        text_key: "title",
        contrast_key: "id",
        keyMap: [{id:"id",title:"title"}],
      },
    }
  ]
}


// // 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改餐时组' : '新增餐时组',
  });
};
setRightA()
setRight()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
