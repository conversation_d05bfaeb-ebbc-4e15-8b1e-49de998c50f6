<template>
  <div class="wrapper-edit">
    <van-field v-model="fieldValue" readonly :border="false" :name="config.key" :label="config.label"
      :placeholder="config.placeholder" :rules="config.rules" :disabled="config.disabled" input-align="right">
    </van-field>
    <div style="border: 1px solid #ccc">
      <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="config.toolbarConfig"
        :mode="config.mode" />
      <Editor :style="{ height: config.height + 'px', 'overflow-y': 'hidden' }" v-model="fieldValue"
        :defaultConfig="config.editorConfig" :mode="config.mode" @onCreated="handleCreated" />
    </div>
  </div>
</template>
<script setup>
import { reactive, watch, ref, getCurrentInstance } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";
import "@wangeditor/editor/dist/css/style.css"; // 引入 css
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
const { proxy } = getCurrentInstance();

let config = {
  // 基础配置
  label: "文本",           // 字段标签 (字符串) - 显示在编辑器上方的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"content", "description"
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用编辑, false: 可正常编辑
  rules: [],               // 验证规则 (数组) - 表单验证规则配置

  // 编辑器配置
  mode: "simple",          // 编辑器模式 (字符串) - "simple": 简洁模式, "default": 默认模式
  height: 300,             // 编辑器高度 (数字) - 编辑器的高度，单位为px

  // 文件上传配置
  baseURL: import.meta.env.VITE_APP_FILE_UPLOAD, // 上传服务器地址 (字符串) - 文件上传的服务器基础URL
  url: "file/post_upload", // 上传接口路径 (字符串) - 文件上传的API接口路径

  // 工具栏配置
  toolbarConfig: {
    excludeKeys: ["|", "insertVideo"], // 排除的工具按钮 (数组) - 不显示的工具栏按钮，如分割线、视频插入
  },

  // 编辑器详细配置
  editorConfig: {
    placeholder: "请输入内容...", // 占位符 (字符串) - 编辑器为空时显示的提示文字
    MENU_CONF: {
      uploadImage: {         // 图片上传配置 (对象) - 自定义图片上传处理逻辑
        customUpload (file, insertFn) {
          proxy
            .$upload(
              config.url,
              {
                file: file,
              },
              { baseURL: config.baseURL }
            )
            .then((res) => {
              if (!res.errcode) {
                file.url = res.result;
                insertFn(res.result, "", "");
              } else {
                throw res.errmsg;
              }
            })
            .catch((err) => {
              console.log(err);
              showToast(err);
            });
        },
      },
    },
  },
};
const props = defineProps({
  config: Object,
  form: Object,
});
props.config && deepAssign(config, props.config);
let fieldValue = ref(props.form[config.key]);
watch(fieldValue, (v, o) => props.form[config.key] = v);
const editorRef = shallowRef();
onBeforeUnmount(() => {
  const { value } = editorRef;
  if (value === null) return;
  value.destroy();
});
const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
  editor.setHtml(fieldValue.value);
};
</script>
<style lang="scss">
.wrapper-edit {
  .van-field__control--right {
    visibility: hidden;
  }
}
</style>
