<template>
  <div class="audit-log-container">
    <!-- 搜索框 -->
    <van-search v-model="searchValue" placeholder="请输入搜索关键词" :clearable="true" />

    <!-- 骨架屏显示 -->
    <div v-if="showSkeleton" class="skeleton-container">
      <van-skeleton v-for="n in 3" :key="n" :row="2" :row-width="['100%', '60%']" :loading="true" :avatar="false"
        :title="true" :title-width="'50%'" class="skeleton-item" />
    </div>

    <!-- 审计日志列表 -->
    <div v-else class="log-list">
      <div v-for="(item, index) in filteredData" :key="item.id" class="audit-log-item" @click="onCardClick(item)">
        <div class="log-content">
          <!-- {{ AURA: Modify - 直接循环渲染审计日志数据 }} -->
          <div class="log-operation">{{ item.operation }}</div>
          <div class="log-details">
            <div class="log-detail-item">
              <span class="detail-label">操作人：</span>
              <span class="detail-value">{{ item.operator }}</span>
            </div>
            <div class="log-detail-item">
              <span class="detail-label">操作时间：</span>
              <span class="detail-value">{{ item.operateTime }}</span>
            </div>
            <div class="log-detail-item">
              <span class="detail-label">IP：</span>
              <span class="detail-value">{{ item.ip }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <van-empty v-if="!showSkeleton && filteredData.length === 0" description="暂无审计日志" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// {{ AURA: Modify - 简化为直接数据渲染模式 }}
// 审计日志模拟数据
const auditLogs = [
  {
    id: 1,
    operation: '管理员张振北进行了充值计划新增操作',
    operator: '张振北',
    operateTime: '2025-07-15 22:42:27',
    ip: '***************'
  },
  {
    id: 2,
    operation: '管理员张振北进行了充值计划删除操作',
    operator: '张振北',
    operateTime: '2025-07-15 22:36:18',
    ip: '***************'
  },
  {
    id: 3,
    operation: '管理员张振北进行了充值计划新增操作',
    operator: '张振北',
    operateTime: '2025-07-15 22:36:05',
    ip: '***************'
  },
  {
    id: 4,
    operation: '管理员张振北进行了充值计划删除操作',
    operator: '张振北',
    operateTime: '2025-07-15 22:35:35',
    ip: '***************'
  },
  {
    id: 5,
    operation: '管理员张振北进行了充值计划删除操作',
    operator: '张振北',
    operateTime: '2025-07-15 22:35:32',
    ip: '***************'
  }
];

// 响应式数据
const searchValue = ref('')
const showSkeleton = ref(true)

// 过滤后的数据（支持搜索）
const filteredData = computed(() => {
  if (!searchValue.value) {
    return auditLogs
  }
  return auditLogs.filter(item =>
    item.operation.includes(searchValue.value) ||
    item.operator.includes(searchValue.value) ||
    item.ip.includes(searchValue.value)
  )
})

// 点击事件 - 跳转到详情页
const onCardClick = (item) => {
  console.log('查看审计日志详情:', item);
  router.push({
    path: '/systemConfig/auditLog/detail',
    query: { id: item.id }
  });
};

// 模拟加载过程
onMounted(() => {
  // 2秒后隐藏骨架屏，显示数据
  setTimeout(() => {
    showSkeleton.value = false
  }, 2000)
})


</script>

<style lang="scss" scoped>
.audit-log-container {
  min-height: 100%;
  background: #f7f8fa;
}

// 骨架屏容器
.skeleton-container {
  padding: 16px;

  .skeleton-item {
    margin-bottom: 16px;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 日志列表容器
.log-list {
  padding: 16px;
  padding-top: 0;
  margin-top: 16px;
}

// {{ AURA: Modify - 审计日志项样式，完全匹配图片效果 }}
.audit-log-item {
  padding: 16px;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 8px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  &:last-child {
    margin-bottom: 0;
  }

  .log-content {
    width: 100%;

    .log-operation {
      font-size: 16px;
      font-weight: bold;
      line-height: 24px;
      color: #323233;
      margin-bottom: 12px;
      word-wrap: break-word;
      word-break: break-all;
    }

    .log-details {
      .log-detail-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
        font-size: 14px;
        line-height: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-label {
          color: #646566;
          font-weight: normal;
          flex-shrink: 0;
          min-width: 80px;
        }

        .detail-value {
          color: #323233;
          font-weight: normal;
          flex: 1;
          word-wrap: break-word;
          word-break: break-all;
        }
      }
    }
  }

  // 点击效果
  &:active {
    background: #f2f3f5;
  }
}
</style>
