# yhc-org-structure 组织架构选择组件

基于钉钉APP组织架构界面设计的组织架构选择组件，提供层级化的部门和人员浏览选择功能。

## 功能特性

- ✅ 仿钉钉APP组织架构界面设计
- ✅ 支持搜索功能
- ✅ 面包屑导航，支持快速跳转
- ✅ 层级化部门浏览
- ✅ 人员信息展示
- ✅ 支持成员选择
- ✅ 响应式设计，适配移动端
- ✅ 部门和人员混合显示，无分隔
- ✅ 空状态上下左右对称居中显示
- ✅ 钉钉H5内部滚动优化
- ✅ 部门名称和人数同行显示
- ✅ 邀请成员加入功能

## 界面特点

### 顶部搜索栏
- 支持按部门名称和人员姓名搜索
- 实时搜索结果展示

### 面包屑导航
- 显示当前浏览路径
- 支持点击快速跳转到上级目录

### 部门列表
- 显示部门图标、名称和人员数量
- 点击进入下级部门

### 人员列表
- 显示人员头像、姓名、职位
- 支持标签显示（主管理员、主管等）
- 点击选择人员

### 邀请成员功能
- 底部显示绿色"邀请成员加入"按钮
- 支持在任何部门层级邀请成员
- 空状态下也显示邀请按钮

## 使用方式

### 1. 作为独立页面使用

```vue
<template>
  <div class="org-page">
    <yhc-org-structure
      @select="handleSelect"
      @invite="handleInvite"
    />
  </div>
</template>

<script setup>
const handleSelect = (selection) => {
  console.log('选择结果:', selection)
  // selection.type: 'member' | 'department'
  // selection.data: 选择的数据对象
}

const handleInvite = (inviteInfo) => {
  console.log('邀请成员:', inviteInfo)
  // inviteInfo.currentDeptId: 当前部门ID
  // inviteInfo.currentDeptName: 当前部门名称
}
</script>
```

### 2. 作为弹窗组件使用

```vue
<template>
  <van-popup v-model:show="showOrgStructure" position="right" :style="{ width: '100%', height: '100%' }">
    <yhc-org-structure
      @select="handleOrgSelect"
      @invite="handleInvite"
    />
  </van-popup>
</template>

<script setup>
import { ref } from 'vue'

const showOrgStructure = ref(false)

const handleOrgSelect = (selection) => {
  console.log('组织架构选择:', selection)
  showOrgStructure.value = false
}

const handleInvite = (inviteInfo) => {
  console.log('邀请成员:', inviteInfo)
  // 可以在这里调用钉钉邀请API
}
</script>
```

### 3. 集成到表单中

```vue
<template>
  <yhc-form :config="formConfig" :form="formData" @onSubmit="handleSubmit" />
</template>

<script setup>
import { reactive } from 'vue'

const formData = reactive({
  selectedMember: null
})

const formConfig = {
  form: [
    {
      component: "yhc-org-structure-field", // 需要额外封装
      key: "selectedMember",
      label: "选择人员",
      placeholder: "请选择人员"
    }
  ]
}
</script>
```

## 事件说明

### @select
当用户选择成员或部门时触发

### @invite
当用户点击"邀请成员加入"按钮时触发

**参数:**
```javascript
{
  type: 'member' | 'department', // 选择类型
  data: {
    // 成员数据结构
    id: 'user1',
    name: '张三',
    title: '主管理员',
    position: '部门经理',
    avatar: 'https://...',
    isManager: true,
    isOwner: false
  }
  // 或部门数据结构
  {
    id: 1,
    name: '技术部',
    memberCount: 10
  }
}
```

**@invite 参数:**
```javascript
{
  currentDeptId: 4,           // 当前部门ID
  currentDeptName: '产品事业部' // 当前部门名称
}
```

## 数据结构

### 部门数据结构
```javascript
{
  id: 1,                    // 部门ID
  name: '技术部',           // 部门名称
  memberCount: 10,          // 部门人员数量
  parentId: 0,              // 父部门ID
  children: []              // 子部门列表
}
```

### 成员数据结构
```javascript
{
  id: 'user1',              // 用户ID
  name: '张三',             // 姓名
  title: '主管理员',        // 职务标题
  position: '部门经理',     // 职位
  avatar: 'https://...',    // 头像URL
  isManager: true,          // 是否为管理员
  isOwner: false,           // 是否为负责人
  deptId: 1                 // 所属部门ID
}
```

## 自定义配置

### 支持的配置项
```javascript
const config = {
  // 是否支持多选
  multiple: false,
  
  // 选择类型限制
  selectType: 'both', // 'member' | 'department' | 'both'
  
  // 根部门ID
  rootDeptId: 0,
  
  // API配置
  api: {
    getDepartments: '/api/departments',
    getMembers: '/api/members',
    search: '/api/org/search'
  }
}
```

## 样式定制

组件使用了CSS变量，支持主题定制：

```css
:root {
  --org-primary-color: #1989fa;
  --org-text-color: #323233;
  --org-secondary-color: #969799;
  --org-border-color: #ebedf0;
  --org-background-color: #f7f8fa;
}
```

## 移动端优化

### 钉钉H5内部使用
组件已针对钉钉H5环境进行了专门优化：

- **滚动优化**: 使用 `-webkit-overflow-scrolling: touch` 确保流畅滚动
- **触摸支持**: 添加 `touch-action` 属性支持移动端手势
- **容器优化**: 使用 `overscroll-behavior: contain` 防止滚动穿透

### 显示优化
- **部门显示**: 部门名称和人数在同一行显示，如 "产品事业部 (13)"
- **混合列表**: 部门和人员在同一个列表中显示，无视觉分隔
- **空状态**: 完全居中的空状态显示

## 注意事项

1. **数据获取**: 当前版本使用模拟数据，实际使用时需要对接真实的组织架构API
2. **权限控制**: 可根据用户权限控制可见的部门和人员范围
3. **性能优化**: 大型组织架构建议使用虚拟滚动或分页加载
4. **缓存策略**: 建议对组织架构数据进行适当缓存以提升用户体验
5. **移动端测试**: 建议在真实的钉钉环境中测试滚动和交互功能

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础的组织架构浏览和选择功能
- 仿钉钉APP界面设计
- 支持搜索和面包屑导航
