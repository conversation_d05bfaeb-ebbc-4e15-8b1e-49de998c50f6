<template>
  <div class="trend-stats">
    <!-- 趋势统计整体区域 -->
    <div class="trend-section">
      <!-- 头部区域 -->
      <div class="header-section">
        <!-- 标题行 -->
        <div class="header-top">
          <div class="trend-title">趋势统计</div>
          <div class="header-right">
            <!-- 筛选按钮 -->
            <div class="filter-selector" @click="openFilterPopup">
              <span class="filter-text">筛选</span>
              <van-icon name="play" size="12" color="#C8C9CC" style="transform: rotate(90deg);" />
            </div>
          </div>
        </div>

        <!-- 已选择筛选条件 -->
        <div class="filter-tags" v-if="hasActiveFilters">
          <span>已选择：</span>
          <span>{{ activeFiltersText }}</span>
        </div>
      </div>

      <!-- 图表整体区域 -->
      <div class="chart-wrapper">
        <!-- 图例 -->
        <div class="chart-legend">
          <div class="legend-item" v-for="line in chartLines" :key="line.key">
            <div class="legend-dot" :style="{ background: line.color }"></div>
            <span>{{ line.name }}</span>
          </div>
        </div>

        <!-- ECharts 图表容器 -->
        <div class="chart-container">
          <div ref="chartRef" class="echarts-chart"></div>
        </div>
      </div>
    </div>

    <!-- 筛选弹窗 -->
    <van-popup
      v-model:show="showFilterPopup"
      round
      position="bottom"
      :style="{ height: '70vh' }"
    >
      <div class="filter-popup">
        <!-- 筛选弹窗头部 -->
        <div class="filter-header">
          <span class="filter-title">筛选条件</span>
        </div>

        <!-- 筛选弹窗主体 -->
        <div class="filter-body">
          <!-- 左侧导航菜单 -->
          <div class="filter-nav">
            <div
              class="filter-nav-item"
              :class="{
                active: activeNavItem === 'dininghall',
                'has-selection': selectedFilters.dininghall !== null
              }"
              @click="scrollToSection('dininghall')"
            >
              <span class="nav-indicator" :class="{ 'has-data': selectedFilters.dininghall !== null }"></span>
              <span class="nav-text">场所</span>
            </div>
            <div
              v-if="mealtimeOptions.length > 0"
              class="filter-nav-item"
              :class="{
                active: activeNavItem === 'mealtime',
                'has-selection': selectedFilters.mealtime.length > 0
              }"
              @click="scrollToSection('mealtime')"
            >
              <span class="nav-indicator" :class="{ 'has-data': selectedFilters.mealtime.length > 0 }"></span>
              <span class="nav-text">餐时</span>
            </div>
            <div
              v-if="stallOptions.length > 0"
              class="filter-nav-item"
              :class="{
                active: activeNavItem === 'stall',
                'has-selection': selectedFilters.stall.length > 0
              }"
              @click="scrollToSection('stall')"
            >
              <span class="nav-indicator" :class="{ 'has-data': selectedFilters.stall.length > 0 }"></span>
              <span class="nav-text">档口</span>
            </div>
          </div>

          <!-- 右侧筛选内容 -->
          <div class="filter-content" ref="filterContentRef">
            <!-- 场所筛选 -->
            <div class="filter-form-item" ref="dininghallRef">
              <div class="filter-label">
                <span class="label-text">场所</span>
              </div>
              <div class="filter-value">
                <div class="filter-select-options">
                  <div
                    v-for="dininghall in dininghallOptions"
                    :key="dininghall.id"
                    class="filter-select-option"
                    :class="{ active: selectedFilters.dininghall === dininghall.id }"
                    @click="selectFilter('dininghall', dininghall.id)"
                  >
                    {{ dininghall.title }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 餐时筛选 -->
            <div class="filter-form-item" v-if="mealtimeOptions.length > 0" ref="mealtimeRef">
              <div class="filter-label">
                <span class="label-text">餐时</span>
              </div>
              <div class="filter-value">
                <div class="filter-select-options">
                  <div
                    v-for="mealtime in mealtimeOptions"
                    :key="mealtime.id"
                    class="filter-select-option"
                    :class="{ active: selectedFilters.mealtime.includes(mealtime.id) }"
                    @click="selectFilter('mealtime', mealtime.id)"
                  >
                    {{ mealtime.title }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 档口筛选 -->
            <div class="filter-form-item" v-if="stallOptions.length > 0" ref="stallRef">
              <div class="filter-label">
                <span class="label-text">档口</span>
              </div>
              <div class="filter-value">
                <div class="filter-select-options">
                  <div
                    v-for="stall in stallOptions"
                    :key="stall.id"
                    class="filter-select-option"
                    :class="{ active: selectedFilters.stall.includes(stall.id) }"
                    @click="selectFilter('stall', stall.id)"
                  >
                    {{ stall.title }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 筛选弹窗底部按钮 -->
        <div class="filter-footer">
          <div class="filter-button reset-button" @click="onFilterReset">
            重置
          </div>
          <div class="filter-button confirm-button" @click="onFilterConfirm">
            确定
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, getCurrentInstance } from 'vue'
import { showToast } from 'vant'
import * as echarts from 'echarts'

const { proxy } = getCurrentInstance()

// 筛选弹窗相关
const showFilterPopup = ref(false)
const activeNavItem = ref('dininghall')
const filterContentRef = ref(null)
const dininghallRef = ref(null)
const mealtimeRef = ref(null)
const stallRef = ref(null)

// 筛选条件 - 当前选择状态（弹窗内的临时状态）
const selectedFilters = reactive({
  dininghall: null,     // 场所ID
  mealtime: [],         // 餐时ID数组
  stall: []             // 档口ID数组
})

// 确认后的筛选状态，用于控制页面显示
const confirmedFilters = reactive({
  dininghall: null,     // 确认后的场所ID
  mealtime: [],         // 确认后的餐时ID数组
  stall: []             // 确认后的档口ID数组
})

// 筛选选项数据
const dininghallOptions = ref([])  // 场所选项
const mealtimeOptions = ref([])    // 餐时选项
const stallOptions = ref([])       // 档口选项

// ECharts 相关
const chartRef = ref(null)
let chartInstance = null

// 图表线条配置
const chartLines = ref([
  { key: 'aStall', name: 'A档口', color: '#1989fa' },
  { key: 'bStall', name: 'B档口', color: '#ff6b6b' },
  { key: 'cStall', name: 'C档口', color: '#4ecdc4' }
])

// 图表数据 - 根据图片显示的数据结构
const chartData = ref([
  { label: '7/29', aStall: 150, bStall: 100, cStall: 50 },
  { label: '7/30', aStall: 170, bStall: 110, cStall: 60 },
  { label: '7/31', aStall: 180, bStall: 105, cStall: 55 },
  { label: '8/1', aStall: 200, bStall: 120, cStall: 65 },
  { label: '8/2', aStall: 210, bStall: 115, cStall: 60 },
  { label: '8/3', aStall: 220, bStall: 130, cStall: 70 },
  { label: '8/4', aStall: 251, bStall: 170, cStall: 75 }
])

// 计算属性
const hasActiveFilters = computed(() => {
  // 只有确认后的筛选条件才显示在页面上
  return confirmedFilters.dininghall !== null ||
         (confirmedFilters.mealtime && confirmedFilters.mealtime.length > 0) ||
         (confirmedFilters.stall && confirmedFilters.stall.length > 0)
})

const activeFiltersText = computed(() => {
  const filters = []

  // 场所筛选
  if (confirmedFilters.dininghall !== null) {
    const dininghall = dininghallOptions.value.find(d => d.id === confirmedFilters.dininghall)
    if (dininghall) {
      filters.push(`场所：${dininghall.title}`)
    }
  }

  // 餐时筛选
  if (confirmedFilters.mealtime && confirmedFilters.mealtime.length > 0) {
    const mealtimeNames = confirmedFilters.mealtime.map(id => {
      const mealtime = mealtimeOptions.value.find(m => m.id === id)
      return mealtime ? mealtime.title : ''
    }).filter(name => name)

    if (mealtimeNames.length > 0) {
      filters.push(`餐时：${mealtimeNames.join('、')}`)
    }
  }

  // 档口筛选
  if (confirmedFilters.stall && confirmedFilters.stall.length > 0) {
    const stallNames = confirmedFilters.stall.map(id => {
      const stall = stallOptions.value.find(s => s.id === id)
      return stall ? stall.title : ''
    }).filter(name => name)

    if (stallNames.length > 0) {
      filters.push(`档口：${stallNames.join('、')}`)
    }
  }

  return filters.join('；')
})

// ECharts 配置
const getChartOption = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      left: '0',
      right: '0',
      top: '5%',
      bottom: '0',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.value.map(item => item.label),
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.85)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(0, 0, 0, 0.85)',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(0, 0, 0, 0.85)',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'solid'
        }
      }
    },
    series: chartLines.value.map(line => ({
      name: line.name,
      type: 'line',
      smooth: true,
      data: chartData.value.map(item => item[line.key]),
      lineStyle: {
        color: line.color,
        width: 2
      },
      itemStyle: {
        color: line.color
      },
      symbol: 'circle',
      symbolSize: 4,
      label: {
        show: false
      },
      emphasis: {
        focus: 'series'
      }
    })),
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e5e5',
      borderWidth: 1,
      textStyle: {
        color: '#323233',
        fontSize: 12
      },
      formatter: function(params) {
        let result = `<div style="font-weight: 500; margin-bottom: 4px;">A场所 - 早餐</div>`
        params.forEach(param => {
          result += `<div style="display: flex; align-items: center; margin-bottom: 2px;">
            <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background: ${param.color}; margin-right: 6px;"></span>
            <span style="margin-right: 8px;">${param.seriesName}</span>
            <span style="font-weight: 500;">${param.value}</span>
          </div>`
        })
        return result
      }
    }
  }
}

// 筛选相关方法
const selectFilter = (type, value) => {
  if (type === 'dininghall') {
    // 场所单选
    selectedFilters[type] = value
    // 场所变更时，清空餐时和档口选择，并重新加载数据
    selectedFilters.mealtime = []
    selectedFilters.stall = []
    if (value) {
      loadMealtimeOptions(value)
      loadStallOptions(value)
    } else {
      mealtimeOptions.value = []
      stallOptions.value = []
    }
  } else if (type === 'mealtime' || type === 'stall') {
    // 餐时和档口多选
    const currentSelection = selectedFilters[type]
    const index = currentSelection.indexOf(value)
    if (index > -1) {
      // 如果已选中，则取消选择
      currentSelection.splice(index, 1)
    } else {
      // 如果未选中，则添加选择
      currentSelection.push(value)
    }
  }
}

const openFilterPopup = () => {
  // 将确认状态复制到选择状态，确保弹窗显示上次确认的选择
  selectedFilters.dininghall = confirmedFilters.dininghall
  selectedFilters.mealtime = [...confirmedFilters.mealtime]
  selectedFilters.stall = [...confirmedFilters.stall]

  // 重置导航状态
  activeNavItem.value = 'dininghall'

  showFilterPopup.value = true
}

const scrollToSection = (sectionName) => {
  activeNavItem.value = sectionName

  // 获取对应的ref元素
  let targetRef = null
  switch (sectionName) {
    case 'dininghall':
      targetRef = dininghallRef.value
      break
    case 'mealtime':
      targetRef = mealtimeRef.value
      break
    case 'stall':
      targetRef = stallRef.value
      break
  }

  // 滚动到目标元素
  if (targetRef && filterContentRef.value) {
    const container = filterContentRef.value
    const target = targetRef
    const containerTop = container.scrollTop
    const targetTop = target.offsetTop - container.offsetTop

    container.scrollTo({
      top: targetTop,
      behavior: 'smooth'
    })
  }
}

const onFilterReset = () => {
  // 获取默认的场所ID（从localStorage缓存）
  const cachedDininghallId = localStorage.getItem('dininghall')
  let defaultDininghallId = null

  if (cachedDininghallId) {
    const cachedId = parseInt(cachedDininghallId)
    const foundDininghall = dininghallOptions.value.find(d => d.id === cachedId)
    if (foundDininghall) {
      defaultDininghallId = cachedId
    }
  }

  // 重置筛选条件，但保留默认场所选择
  selectedFilters.dininghall = defaultDininghallId
  selectedFilters.mealtime = []
  selectedFilters.stall = []

  // 如果有默认场所，重新加载对应的餐时和档口数据
  if (defaultDininghallId) {
    loadMealtimeOptions(defaultDininghallId)
    loadStallOptions(defaultDininghallId)
  } else {
    // 如果没有默认场所，清空餐时和档口选项
    mealtimeOptions.value = []
    stallOptions.value = []
  }
}

const onFilterConfirm = () => {
  // 确认时将选择的筛选条件复制到确认状态
  confirmedFilters.dininghall = selectedFilters.dininghall
  confirmedFilters.mealtime = [...selectedFilters.mealtime]
  confirmedFilters.stall = [...selectedFilters.stall]

  showFilterPopup.value = false
  // 重新加载趋势数据
  loadTrendData()
}

const initChart = () => {
  if (chartRef.value && !chartInstance) {
    chartInstance = echarts.init(chartRef.value)
    updateChart()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  }
}

const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(getChartOption(), true)
  }
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const destroyChart = () => {
  if (chartInstance) {
    window.removeEventListener('resize', handleResize)
    chartInstance.dispose()
    chartInstance = null
  }
}

// 数据加载方法 - 使用与KeyDataStats相同的API接口
const loadDininghallOptions = async () => {
  try {
    const response = await proxy.$get('/dininghall/get_all', { type: 0 })
    if (response.code === 200) {
      dininghallOptions.value = response.data.map(item => ({
        id: item.id,
        title: item.title
      }))

      // 设置默认选择本地缓存的dininghall_id
      const cachedDininghallId = localStorage.getItem('dininghall')
      if (cachedDininghallId) {
        const cachedId = parseInt(cachedDininghallId)
        const foundDininghall = dininghallOptions.value.find(d => d.id === cachedId)
        console.log('找到的餐厅:', foundDininghall) // 调试日志
        if (foundDininghall) {
          // 设置筛选弹窗的默认选择，同时设置确认状态显示在页面上
          selectedFilters.dininghall = cachedId
          confirmedFilters.dininghall = cachedId
          console.log('设置默认餐厅ID:', cachedId) // 调试日志
          // 加载对应的餐时和档口数据
          await loadMealtimeOptions(cachedId)
          await loadStallOptions(cachedId)
        }
      }
    } else {
      console.warn('场所API调用失败，使用测试数据:', response.message)
      // 使用测试数据
      dininghallOptions.value = [
        { id: 1, title: 'A餐厅' },
        { id: 2, title: 'B餐厅' },
        { id: 3, title: 'C餐厅' }
      ]
    }
  } catch (error) {
    console.error('加载场所数据失败:', error)
    // API调用异常时，使用测试数据
    dininghallOptions.value = [
      { id: 1, title: 'A餐厅' },
      { id: 2, title: 'B餐厅' },
      { id: 3, title: 'C餐厅' }
    ]
  }
}

const loadMealtimeOptions = async (dininghallId) => {
  try {
    const response = await proxy.$get('/mealtime/get_all', { dininghall_id: dininghallId })
    if (response.code === 200) {
      mealtimeOptions.value = response.data.map(item => ({
        id: item.id,
        title: item.title
      }))
    } else {
      console.warn('餐时API调用失败，使用测试数据:', response.message)
      mealtimeOptions.value = [
        { id: 1, title: '早餐' },
        { id: 2, title: '午餐' },
        { id: 3, title: '晚餐' }
      ]
    }
  } catch (error) {
    console.error('加载餐时数据失败:', error)
    mealtimeOptions.value = [
      { id: 1, title: '早餐' },
      { id: 2, title: '午餐' },
      { id: 3, title: '晚餐' }
    ]
  }
}

const loadStallOptions = async (dininghallId) => {
  try {
    const response = await proxy.$get('/stall/get_all', { dininghall_id: dininghallId })
    if (response.code === 200) {
      stallOptions.value = response.data.map(item => ({
        id: item.id,
        title: item.title
      }))
    } else {
      console.warn('档口API调用失败，使用测试数据:', response.message)
      stallOptions.value = [
        { id: 1, title: 'A档口' },
        { id: 2, title: 'B档口' },
        { id: 3, title: 'C档口' },
        { id: 4, title: 'D档口' }
      ]
    }
  } catch (error) {
    console.error('加载档口数据失败:', error)
    stallOptions.value = [
      { id: 1, title: 'A档口' },
      { id: 2, title: 'B档口' },
      { id: 3, title: 'C档口' },
      { id: 4, title: 'D档口' }
    ]
  }
}

// 加载趋势数据
const loadTrendData = async () => {
  try {
    const response = await proxy.$post('statistics/trend_data', {
      dininghall_id: confirmedFilters.dininghall,
      mealtime_ids: confirmedFilters.mealtime,
      stall_ids: confirmedFilters.stall
    })

    if (!response.errcode) {
      chartData.value = response.result.chartData || chartData.value
      // 数据更新后重新渲染图表
      nextTick(() => {
        updateChart()
      })
    } else {
      showToast(response.errmsg || '获取趋势数据失败')
    }
  } catch (error) {
    console.error('加载趋势数据失败:', error)
    showToast('获取趋势数据失败')
  }
}

// 生命周期
onMounted(async () => {
  await loadDininghallOptions()
  await loadTrendData()
  await nextTick()
  initChart()
})

onUnmounted(() => {
  destroyChart()
})
</script>

<style lang="scss" scoped>
.trend-stats {
  padding: 0 16px;
}

.trend-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

// 头部区域样式
.header-section {
  margin-bottom: 16px;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.trend-title {
  font-size: 15px;
  font-weight: 500;
  color: #323233;
  line-height: 21px;
}

.filter-selector {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
 
}

.filter-text {
  font-size: 14px;
  color: #1989FA;
  line-height: 20px;
}

.filter-tags {
  padding: 8px;
  background: #F7F8FA;
  border-radius: 8px;
  font-size: 12px;
  color: #969799;
  line-height: 16px;
}

.chart-wrapper {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 16px;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #646566;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.chart-container {
  margin: 0;
}

.echarts-chart {
  width: 100%;
  height: 280px;
}

// 筛选弹窗样式 - 与KeyDataStats保持一致
.filter-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f7f8fa;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #ebedf0;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

/* {{ AURA-X: Add - 左右分栏布局 }} */
.filter-body {
  display: flex;
  flex: 1;
  overflow: hidden;
  z-index: 1000;
}

/* {{ AURA-X: Add - 左侧导航样式 }} */
.filter-nav {
  width: 80px;
  background: #f2f3f5;
  overflow-y: auto;
}

/* {{ AURA-X: Modify - 重新设计导航项样式，支持红色指示点和完整白色背景 }} */
.filter-nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  line-height: 19px;
  cursor: pointer;
  font-size: 13px;
  color: #323233;
  &.active {
    background: #fff;

    .nav-text {
      color: #323233;
    }
  }

}

/* {{ AURA-X: Add - 导航指示器样式，支持红色数据指示 }} */
.nav-indicator {
  position: absolute;
  top: 26px;
  left: 19px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: transparent;
  transition: all 0.2s;

  &.has-data {
    background: #ff4444;
  }
}

.nav-text {
  font-size: 13px;
  color: #646566;
  text-align: center;
  line-height: 19px;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
  background: #fff;
}

/* {{ AURA-X: Add - 表单项样式，类似图片中的设计 }} */
.filter-form-item {
  background: #fff;
  overflow: hidden;
}

.filter-label {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #fff;
}

.label-text {
  font-size: 14px;
  line-height: 20px;
  color: #323233;
}

.filter-value {
  padding: 0 16px;
  background: #fff;
}

.filter-select-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-select-option {
  padding: 6px 12px;
  background: #F2F3F5;
  border-radius: 8px;
  font-size: 13px;
  line-height: 20px;
  color: #323233;
  cursor: pointer;
  min-width: 82px;
  text-align: center;

  &.active {
    background: rgba(25, 137, 250, 0.1);
    color: #1989FA;
  }
}

/* {{ AURA-X: Modify - 重新设计底部按钮样式 }} */
.filter-footer {
  display: flex;
  padding: 5px 16px;
  padding-bottom: 16px;
  gap: 12px;
  background: #fff;
}

.filter-button {
  flex: 1;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 22px;
  font-size: 16px;
}

.reset-button {
  color: #323233;
  border: 0.5px solid #DCDEE0;
}

.confirm-button {
  background: #1989fa;
  color: #fff;
}
</style>
