<template>
  <div class="wrapper-list-com" v-if="data.initStatus">
    <van-search v-model="data.searchValue" v-if="config.search.isShow" :show-action="config.search.isShowPopup"
      placeholder="请输入搜索关键词" :clearable="true" @search="onSearch" @update:model-value="onSearch" shape="round">
      <template #action v-if="config.search.isShowPopup">
        <div @click="data.filterbottom = true" v-if="config.search.filtrateBottom">筛选</div>
        <div @click="data.showPopup = true" v-else>筛选</div>
      </template>
    </van-search>

    <!-- 头部插槽：用于在搜索和列表之间添加内容 -->
    <slot name="header"></slot>

    <!-- 骨架屏显示 -->
    <div v-if="data.showSkeleton && config.skeleton.isShow" class="skeleton-container">
      <van-skeleton v-for="n in config.skeleton.count" :key="n" :row="config.skeleton.row"
        :row-width="config.skeleton.rowWidth" :loading="true" :avatar="config.skeleton.avatar"
        :avatar-size="config.skeleton.avatarSize" :avatar-shape="config.skeleton.avatarShape"
        :title="config.skeleton.title" :title-width="config.skeleton.titleWidth" class="skeleton-item" />
    </div>

    <!-- 正常列表内容 -->
    <div v-else>
      <van-tabs v-if="config.tabs.isShow" v-model:active="active" line-width="16px" color="#000000"
        :sticky="config.tabs.sticky" :swipeable="config.tabs.swipeable" @click-tab="onClickTab">
        <slot name="tab-below" v-if="config.tabs.isShow"></slot>
        <van-tab v-for="(tab, i) in config.tabs.list" :title="tab.text" :key="i">
          <van-list v-model:loading="data.loading" v-model:error="data.error" error-text="请求失败，点击重新加载"
            :finished="data.finished" finished-text="没有更多了" @load="onLoad">
            <template v-for="(item, i) in data.list" :key="i">
              <slot :item="item" :index="i" :tab="tab" :list="data.list"></slot>
            </template>
          </van-list>
        </van-tab>
      </van-tabs>

      <!-- tab下方插槽：用于在tab选择下方添加内容 -->

      <van-list v-else v-model:loading="data.loading" v-model:error="data.error" error-text="请求失败，点击重新加载"
        :finished="data.finished" finished-text="没有更多了" @load="onLoad">
        <template v-for="(item, i) in data.list" :key="i" v-if="config.sort === false">
          <slot :item="item" :index="i" :list="data.list"></slot>
        </template>
        <!-- 菜品配置，菜品分类排序 -->
        <template v-else>
          <draggable v-model="data.list" handle=".move" @start="onStart" @end="onEnd" animation="300" item-key="id">
            <template #item="{ element }">
              <div class="dish-card" @click.stop="onCardClick(element)" v-if="config.title === '菜品配置'">
                <div class="move" v-if="sort_type">
                  <img src="/public/img/move.svg" alt=""
                    style="width: 12px; height: 20px; margin-top: 50px; margin-right: 10px;" />
                </div>
                <div class="card-image">
                  <div style=" width: 72px;
                    height: 72px;
                    border-radius: 8px;
                    line-height: 80px;
                    text-align: center;
                    background-color: #007fff;
                    color: #fff;
                    font-size: 27px;" v-if="element.image == ''">{{ element.title[0] }}</div>
                  <van-image width="72" height="72" radius="8" :src="element.image" fit="cover" :show-loading="false"
                    v-else />
                </div>
                <div class="card-content">
                  <div class="card-header">
                    <span class="dish-title">{{ element.title }}</span>
                    <van-tag plain type="primary" class="dish-tag">
                      {{ element.category_title }}
                    </van-tag>
                  </div>
                  <div class="dish-desc">{{ element.category_title }}</div>
                  <div class="dish-price">￥{{ element.price }}</div>
                </div>
              </div>
              <div class="dish-card" @click.stop="onCardClick(element)" v-else-if="config.title === '表单设计'">
                <div class="move" v-if="sort_type">
                  <img src="/public/img/move.svg" alt="" style="width: 10px; height: 15px;  margin-top: 20px;" />
                  <div style="width: 10px;"></div>
                </div>
                <div class="card-contentA">
                  <div class="card-header">
                    <span class="dish-title">{{ element.field_title }}</span>
                    <div class="dish-A" v-if="element.is_required == 1">
                      必填
                    </div>
                  </div>
                </div>
              </div>
              <div class="demo-item" @click.stop="onCardClick(element)" v-else>
                <div class="move" v-if="sort_type">
                  <img src="/public/img/move.svg" alt=""
                    style="width: 15px; margin-top: 20px; margin-right: 10px; width: 8px;height: 16px;" />
                </div>
                <div class="item-content">
                  <div class="item-title">{{ element.title }}</div>
                </div>
              </div>
            </template>
          </draggable>
        </template>
      </van-list>
    </div>
    <van-popup v-model:show="data.showPopup" :round="config.popup.round" :position="config.popup.position"
      :style="config.popup.style" :closeable="config.popup.closeable">
      <yhc-form :form="form" :config="config.filter" @onSubmit="onFilterSubmit" />
    </van-popup>

    <!-- 底部弹出 -->
    <van-popup v-model:show="data.filterbottom" position="bottom" round :style="{ height: '571px' }">
      <div style="margin-bottom: 14px;">
        <div
          style="position: fixed;margin-top: -46px;border-top-left-radius: 20px;border-top-right-radius: 20px; font-size: 16px;line-height: 22px;color: #323233;height: 46px;display: flex;align-items: center;justify-content: center; width: 100%;border-bottom: 1px solid #f5f5f5;background-color: #fff;z-index: 1000;">
          全部筛选
        </div>
        <div style="display: flex;margin-top: 46px;">
          <div style="width: 30vw;background-color: #F2F3F5;height: 571px;padding-left: 5px;">
            <div style="height: 60px;font-size: 13px;line-height: 80px;color: #646566;"
              @click="scrollToSection('date')">日期
            </div>
            <div style="height: 60px;font-size: 13px;line-height: 80px;color: #646566;"
              @click="scrollToSection('amount')">
              金额</div>
            <div style="height: 60px;font-size: 13px;line-height: 80px;color: #646566;"
              @click="scrollToSection('income')">
              收支</div>
            <div style="height: 60px;font-size: 13px;line-height: 80px;color: #646566;"
              @click="scrollToSection('changeType')">变动类型</div>
          </div>
          <div style="padding: 16px;">
            <div ref="dateSection">
              <div style="font-size: 14px;line-height: 20px;">日期</div>
              <div>
                <div
                  style="font-size: 16px; color: #C8C9CC;width: 72vw;border: 1px solid #C8C9CC;height: 36px;display: flex; align-items: center;padding-left: 5px;border-radius: 10px;margin-top: 16px;"
                  @click="showStartCalendar = true">
                  <van-icon name="underway-o" /><span style="color: #000;margin-left: 9px;"
                    v-if="filtrateData.start_time">{{
                      filtrateData.start_time }}</span> <span v-else style="margin-left: 9px;">开始日期</span>
                </div>
                <div
                  style="font-size: 16px; color: #C8C9CC;width: 72vw;border: 1px solid #C8C9CC;height: 36px;display: flex; align-items: center;padding-left: 5px;border-radius: 10px;margin-top: 16px;"
                  @click="showEndCalendar = true">
                  <van-icon name="underway-o" /><span style="color: #000;margin-left: 9px;"
                    v-if="filtrateData.end_time">{{
                      filtrateData.end_time }}</span> <span v-else style="margin-left: 9px;">结束日期</span>
                </div>
              </div>
            </div>
            <div ref="amountSection">
              <div style="font-size: 14px;line-height: 20px;margin-top: 14px;">金额</div>
              <div>
                <div class="amount-range-fields" style="margin-top: 16px;">
                  <div style="display: flex; align-items: center; border: 1px solid #e6e6e6; color: #c8c9cc;
        border-radius: 6px;">
                    <span style="margin-left: 8px; color: #323233;">￥</span>
                    <van-field v-model="filtrateData.min_amount" placeholder="最小金额" type="number"
                      class="amount-field" />
                  </div>
                  <span class="amount-sep">-</span>
                  <div style="display: flex; align-items: center; border: 1px solid #e6e6e6; color: #c8c9cc;
        border-radius: 6px;">
                    <span style="margin-left: 8px; color: #323233;">￥</span>
                    <van-field v-model="filtrateData.max_amount" placeholder="最大金额" type="number"
                      class="amount-field" />
                  </div>
                </div>
              </div>
            </div>
            <div ref="incomeSection">
              <div style="font-size: 14px;line-height: 20px;margin-top: 14px;">收支</div>
              <div>
                <div class="income-type-options">
                  <van-button size="large"
                    :style="{ color: filtrateData.type === null ? '#1989FA' : '#323233', backgroundColor: filtrateData.type === null ? 'rgba(25, 137, 250, 0.1)' : '#F2F3F5', border: 'none' }"
                    @click="filtrateData.type = null" class="income-type-btn">全部</van-button>
                  <!-- :type="filtrateData.type === 0 ? 'primary' : 'default'" -->
                  <van-button size="large"
                    :style="{ color: filtrateData.type === 0 ? '#1989FA' : '#323233', backgroundColor: filtrateData.type === 0 ? 'rgba(25, 137, 250, 0.1)' : '#F2F3F5', border: 'none' }"
                    @click="filtrateData.type = 0" class="income-type-btn">收入</van-button>
                  <van-button size="large"
                    :style="{ color: filtrateData.type === 1 ? '#1989FA' : '#323233', backgroundColor: filtrateData.type === 1 ? 'rgba(25, 137, 250, 0.1)' : '#F2F3F5', border: 'none' }"
                    @click="filtrateData.type = 1" class="income-type-btn">支出</van-button>
                </div>
              </div>
            </div>
            <div ref="incomeSection">
              <div style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
                <div style="font-size: 14px;line-height: 20px;margin-top: 14px;">变动类型</div>
                <van-button size="small" type="default" @click="showMoreChangeType = !showMoreChangeType"
                  class="more-btn" style="border: none;color: #969799;padding-top: 14px;" v-if="showMoreChangeType">
                  收起<van-icon name="arrow-up" /></van-button>
              </div>
              <div>
                <div class="filter-content-right">
                  <div class="change-type-grid" style="font-size: 13px;">
                    <!-- :type="filtrateData.changeType === tag.value ? 'primary' : 'default'" -->
                    <van-button v-for="tag in visibleChangeTypeTags" :key="tag.value" size="small"
                      :style="{ color: filtrateData.changeType === tag.value ? '#1989FA' : '#323233', backgroundColor: filtrateData.changeType === tag.value ? 'rgba(25, 137, 250, 0.1)' : '#F2F3F5', border: 'none' }"
                      @click="selectChangeType(tag.value)" class="change-type-btn">{{ tag.label }}</van-button>
                    <van-button size="small" type="default" @click="showMoreChangeType = !showMoreChangeType"
                      class="more-btn" style="border: none;color: #969799;" v-if="!showMoreChangeType"> 更多<van-icon
                        name="arrow-down" /></van-button>
                    <!-- <van-button size="small" type="default" @click="showMoreChangeType = !showMoreChangeType"
                    class="more-btn" style="border: none;color: #969799;" v-if="showMoreChangeType"> 收起<van-icon
                      name="arrow-up" /></van-button> -->
                  </div>
                  <div class="more-options" v-if="changeTypeTags.length > 6">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="filter-popup-actions">
          <van-button type="default" block @click="resetFilter">重置</van-button>
          <van-button type="primary" block @click="confirmFilter">确定</van-button>
        </div>
      </div>
    </van-popup>
    <!-- 开始时间选择器 -->
    <van-popup v-model:show="showStartCalendar" position="bottom">
      <van-picker :columns="dateColumns" title="选择开始时间" @confirm="confirmStartDate"
        @cancel="showStartCalendar = false" />
    </van-popup>
    <!-- 结束时间选择器 -->
    <van-popup v-model:show="showEndCalendar" position="bottom">
      <van-picker :columns="dateColumns" title="选择结束时间" @confirm="confirmEndDate" @cancel="showEndCalendar = false" />
    </van-popup>
    <div class="button-wrap" v-if="config.button.isShow">
      <van-button block type="primary" @click="onButClick">
        {{ config.button.text }}
      </van-button>
    </div>
  </div>
</template>
<script setup>
import { reactive, getCurrentInstance, watch,onMounted } from "vue";
import { deepAssign } from "@/untils";
import { showToast } from "vant";
import { useRouter } from 'vue-router'
import { useLoginStore } from "@/store/dingLogin";
import draggable from "vuedraggable"
const router = useRouter();
let showStartCalendar = ref(false)
let showEndCalendar = ref(false)
const app = useLoginStore();
const { proxy } = getCurrentInstance();
const props = defineProps({
  config: Object,
  filterForm:Object
});
const sortableList = ref([]);
const sort_type = ref(false)
onMounted(() => { 
  if(props.filterForm){
  filtrateData.value =JSON.parse(JSON.stringify(props.filterForm)) 
  console.log(filtrateData.value,8886)
}
});
const safeParseInt = (value) => {
  const parsed = parseInt(value);
  return isNaN(parsed) ? null : parsed;
};
function confirmFilter() {
  filtrateData.value.min_amount = safeParseInt(filtrateData.value.min_amount);
  filtrateData.value.max_amount = safeParseInt(filtrateData.value.max_amount);
  filtrateData.value.account_type = parseInt(filtrateData.value.account_type);

  initData();
  form = filtrateData.value;
  onLoad();

  console.log("筛选条件:", filtrateData.value);

  // 触发筛选提交事件
  // emits("onFilterSubmit", filtrateData.value);
}
function resetFilter(){
  filtrateData.value =JSON.parse(JSON.stringify(props.filterForm)) 
}
const config = reactive({
  // API接口配置
  curl: {
    ls: "",        // 列表数据获取接口地址 (必填) - 例: "/api/list"
    sort: "",      // 排序接口地址 (可选) - 例: "/api/sort" - 当sort为true时必填
  },
  details: '',     // 详情页路由地址 (可选) - 例: "/detail" - 点击列表项时跳转
  title: '',       // 列表标题 (可选) - 用于特殊样式判断，如"菜品配置"、"表单设计"
  sort: false,     // 是否启用拖拽排序功能 (布尔值) - true: 启用拖拽排序, false: 普通列表
  postData: {},    // 请求参数 (对象) - 发送给接口的额外参数，如: {category_id: 1, status: 1}

  // 搜索配置
  search: {
    isShow: true,           // 是否显示搜索框 (布尔值) - true: 显示, false: 隐藏
    isShowPopup: true,      // 是否显示筛选按钮 (布尔值) - true: 显示"筛选"按钮, false: 仅搜索
    key: 'title',          // 搜索字段名 (字符串) - 搜索时使用的字段名，如: 'title', 'name', 'keyword'
    placeholder: '请输入搜索关键词', // 搜索框占位符 (字符串) - 搜索框提示文字
    filtrateBottom: false,  // 是否显示筛选按钮 (布尔值) - true: 显示"筛选"按钮, false: 仅搜索
  },

  // 筛选弹窗配置
  popup: {
    round: false,          // 是否圆角弹窗 (布尔值) - true: 圆角, false: 直角
    position: "right",     // 弹窗位置 (字符串) - "right": 右侧, "bottom": 底部, "center": 居中
    style: { width: "90vw", height: "100%", overflow: "hidden" }, // 弹窗样式 (对象) - CSS样式对象
    closeable: false,      // 是否显示关闭按钮 (布尔值) - true: 显示X按钮, false: 不显示
  },
  // 筛选表单配置
  filter: {
    curl: {
      add: "",           // 筛选提交接口地址 (字符串) - 例: "/api/filter" - 筛选表单提交时调用
    },
    postData: {},        // 筛选表单额外参数 (对象) - 提交筛选时的额外参数
    search: {},          // 筛选搜索配置 (对象) - 筛选表单的搜索相关配置
    groupForm: [         // 表单分组配置 (数组) - 控制表单字段的分组显示
      [0, 2],           // 第一组: 显示索引0-1的字段
      [2, 6],           // 第二组: 显示索引2-5的字段
    ],
    form: [],           // 筛选表单字段配置 (数组) - yhc-form组件的表单字段配置
    button: {
      text: "提交",      // 筛选提交按钮文字 (字符串) - 默认"提交"
    },
  },

  // 标签页配置
  tabs: {
    isShow: true,        // 是否显示标签页 (布尔值) - true: 显示标签切换, false: 不显示
    sticky: true,        // 是否粘性定位 (布尔值) - true: 滚动时标签栏固定在顶部, false: 跟随滚动
    swipeable: false,    // 是否支持手势滑动切换 (布尔值) - true: 支持左右滑动切换, false: 仅点击切换
    list: [              // 标签列表配置 (数组) - 标签页数据
      // {
      //   text: "全部",   // 标签显示文字 (字符串) - 标签页显示的文本
      //   key: "all",     // 标签唯一标识 (字符串/数字) - 用于区分不同标签页
      // },
    ],
  },

  // 底部按钮配置
  button: {
    isShow: true,        // 是否显示底部按钮 (布尔值) - true: 显示, false: 隐藏
    text: "新增",        // 按钮文字 (字符串) - 按钮显示的文本，如"新增"、"提交"、"保存"
  },
  // 骨架屏配置 - 数据加载时的占位效果
  skeleton: {
    isShow: false,       // 是否启用骨架屏 (布尔值) - true: 启用加载占位效果, false: 不显示
    count: 5,           // 骨架屏数量 (数字) - 显示多少个骨架屏占位项，建议3-8个
    row: 3,             // 每个骨架屏的行数 (数字) - 每个占位项包含的行数，建议2-4行
    rowWidth: ['100%', '60%', '80%'], // 每行的宽度 (数组) - 每行占位条的宽度，模拟真实内容长度
    avatar: true,       // 是否显示头像占位 (布尔值) - true: 显示圆形头像占位, false: 不显示
    avatarSize: '40px', // 头像大小 (字符串) - 头像占位的尺寸，如'40px', '50px'
    avatarShape: 'round', // 头像形状 (字符串) - 'round': 圆形, 'square': 方形
    title: true,        // 是否显示标题占位 (布尔值) - true: 显示标题占位条, false: 不显示
    titleWidth: '50%',  // 标题宽度 (字符串) - 标题占位条的宽度，如'50%', '80px'
    duration: 2000,     // 骨架屏显示时长 (数字) - 毫秒，超过此时间自动隐藏，0表示不自动隐藏
  },
});
let filtrateData = ref({})
const data = reactive({
  initStatus: false,
  searchValue: "",
  showPopup: false,
  filterbottom: false,
  list: [],
  loading: false,
  finished: false,
  error: false,
  showSkeleton: false, // 控制骨架屏显示
  page: {
    page: 1,
    per_page: 20,
  },
});
let form = {};
let active =
  app.listCofig && app.listCofig.reportOrOrder
    ? ref(app.listCofig.reportOrOrder)
    : ref(0);
// 初始化配置
props.config && deepAssign(config, props.config);

// 监听配置变化
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    // 直接更新skeleton配置
    if (newConfig.skeleton) {
      Object.assign(config.skeleton, newConfig.skeleton);
    }
    // 更新其他配置
    deepAssign(config, newConfig);
    console.log('yhc-list配置已更新:', config.skeleton);
  }
}, { deep: true, immediate: true });

let emits = defineEmits(["onButClick", "onClickTab"]);
const initData = () => {
  data.showPopup = false;
  data.list = [];
  data.page.page = 1;
  data.finished = false;
  data.error = false;
  data.showSkeleton = false;
  data.filterbottom = false;
};
// 显示骨架屏
const showSkeletonScreen = () => {
  data.showSkeleton = true;
  // 获取当前最新的显示时长
  const currentDuration = config.skeleton.duration || 2000;
  console.log('骨架屏显示时长:', currentDuration);

  // 设置骨架屏显示时长
  setTimeout(() => {
    data.showSkeleton = false;
    console.log('骨架屏隐藏');
  }, currentDuration);
};

const filterContentRef = ref(null)
const dateSection = ref(null)
const amountSection = ref(null)
const incomeSection = ref(null)
const changeTypeSection = ref(null)
// 滚动到指定区域的方法
const scrollToSection = (section) => {

  const sectionMap = {
    'date': dateSection.value,
    'amount': amountSection.value,
    'income': incomeSection.value,
    'changeType': changeTypeSection.value
  }

  const targetElement = sectionMap[section]
  if (targetElement && filterContentRef.value) {
    // 计算目标元素相对于容器的位置
    const container = filterContentRef.value
    const offsetTop = targetElement.offsetTop

    // 平滑滚动到目标位置
    container.scrollTo({
      top: offsetTop,
      behavior: 'smooth'
    })
  }
}
function isFiltrate() {

}

const onLoad = (postData = {}) => {
  // 如果是首次加载且启用了骨架屏，显示骨架屏
  if (data.page.page === 1 && config.skeleton.isShow && data.list.length === 0) {
    showSkeletonScreen();
  }

  data.loading = true;
  let query = { ...data.page, ...props.config.postData, ...postData, ...form };
  data.page.page++;
  if (data.searchValue) {
    // console.log("query.config.value.search", config.search.key);
    // 已知config.search.key是字段名在query对象中加入这个字段名
    query[config.search.key] = data.searchValue;
  }

  // // 检查是否为演示模式（没有真实API）
  // if (!config.curl.ls || config.mockData) {
  //   // 模拟数据模式
  //   // 使用骨架屏显示时长作为模拟网络延迟，确保时序一致
  //   const mockDelay = Math.max(config.skeleton.duration || 2000, 500); // 至少500ms
  //   console.log('模拟数据加载延迟:', mockDelay);

  //   setTimeout(() => {
  //     let mockData = []

  //     // 如果配置中提供了模拟数据，使用配置的数据
  //     if (Array.isArray(config.mockData)) {
  //       mockData = config.mockData
  //     } else {
  //       // 默认模拟数据
  //       mockData = [
  //         {
  //           id: 1,
  //           title: '示例数据 #001',
  //           description: '这是一个示例数据项',
  //           time: '2024-01-15 12:30:00'
  //         },
  //         {
  //           id: 2,
  //           title: '示例数据 #002',
  //           description: '这是另一个示例数据项',
  //           time: '2024-01-15 18:45:00'
  //         }
  //       ]
  //     }

  //     data.list.push(...mockData);
  //     if (config.format) {
  //       config.format(data.list);
  //     }
  //     data.finished = true; // 模拟数据只有一页
  //     data.loading = false;
  //     data.showSkeleton = false;
  //   }, mockDelay);
  //   return;
  // }

  // console.log("请求数据------》", config.curl, query);
  proxy
    .$get(config.curl.ls, query)
    .then((res) => {
      if (res.code === 200) {
        res = res.data;
        console.log("请求数据------》", res.items);
        if (res.items && res.items.length) {
          if (config.format) {
            config.format(res.items);
          }
          data.list.push(...res.items);
          sortableList.value = data.list;
        } else if (res.items === null) {
          // 当items为null时，表示没有更多数据，正常结束加载
          data.finished = true;
          data.error = false; // 确保不显示错误状态
        } else {
          // 当items为空数组时，也表示没有更多数据
          data.finished = true;
          data.error = false; // 确保不显示错误状态
        }
      } else {
        data.error = true;
        throw res.msg;
      }
    })
    .catch((err) => {
      data.error = true;
      showToast(err);
    })
    .finally(() => {
      data.loading = false;
      // 确保骨架屏在请求完成后隐藏
      data.showSkeleton = false;
    });
};
// 变动类型标签
const changeTypeTags = [
  { label: '补贴发放', value: 'subsidy-recharge', type: 1 },
  { label: '补贴扣除', value: 'subsidy-arrive', type: 2 },
  { label: '支付宝充值', value: 'alipay-save', type: 3 },
  { label: '支付宝提现', value: 'alipay-refund', type: 4 },
  { label: '微信充值', value: 'wechat-save', type: 5 },
  { label: '微信提现', value: 'wechat-refund', type: 6 },
  { label: '转账收款', value: 'transfer-receive', type: 10 },
  { label: '转账扣款', value: 'transfer-deduct', type: 9 },
  { label: '消费支出', value: 'consume-out', type: 7 },
  { label: '退款收入', value: 'refund-income', type: 8 }
]

const showMoreChangeType = ref(false)

// 计算可见的变动类型标签
const visibleChangeTypeTags = computed(() => {
  if (showMoreChangeType.value) {
    return changeTypeTags
  }
  return changeTypeTags.slice(0, 5)
})
// 选择变动类型
const selectChangeType = (type) => {
  filtrateData.value.changeType = filtrateData.value.changeType === type ? '' : type
}
const onButClick = (e) => {
  // console.log("按钮点击----->", e);
  emits("onButClick", e, active.value);
};
const onSearch = (e) => {
  // console.log("搜索事件----->", e);
  initData();
  onLoad();
};
const onFilterSubmit = (filterData) => {
  // console.log("筛选事件----->", filterData);
  initData();
  form = filterData;
  onLoad();
};
watch(active, (v) => {
  if (app.listCofig) {
    app.listCofig.reportOrOrder = v;
  } else {
    app.listCofig = {
      reportOrOrder: v,
    };
  }
});
const onClickTab = (tabData) => {
  // console.log("tab事件----->", tabData);
  initData();
  let postData = JSON.parse(JSON.stringify(config.tabs.list[tabData.name]));
  delete postData.text;
  onLoad(postData);
  emits("onClickTab", tabData);
};
const del = (e) => {
  data.loading = true;
  proxy
    .$post(config.curl.del, e)
    .then((res) => {
      if (res.code === 200) {
        showToast("操作成功~");
        onSearch();
      } else {
        data.error = true;
        throw res.msg;
      }
    })
    .catch((err) => {
      data.error = true;
      showToast(err);
    })
    .finally(() => {
      data.loading = false;
    });
};
// 生成日期选择器的列数据
const generateDateColumns = () => {
  const currentYear = new Date().getFullYear()
  const years = []
  const months = []
  const days = []

  // 生成年份（当前年份前后5年）
  for (let i = currentYear - 5; i <= currentYear + 5; i++) {
    years.push({ text: `${i}年`, value: i })
  }

  // 生成月份
  for (let i = 1; i <= 12; i++) {
    months.push({ text: `${i}月`, value: i })
  }

  // 生成日期
  for (let i = 1; i <= 31; i++) {
    days.push({ text: `${i}日`, value: i })
  }

  return [years, months, days]
}

const dateColumns = generateDateColumns()

// 确认开始日期
const confirmStartDate = ({ selectedValues }) => {
  console.log('开始日期选择值:', selectedValues)
  const formattedDate = formatPickerDate(selectedValues)
  console.log('格式化后的开始日期:', formattedDate)

  // 强制更新响应式数据
  filtrateData.value = {
    ...filtrateData.value,
    start_time: formattedDate
  }

  console.log('更新后的 filtrateData:', filtrateData.value)
  showStartCalendar.value = false
}
// 确认结束日期
const confirmEndDate = ({ selectedValues }) => {
  console.log('结束日期选择值:', selectedValues)
  const formattedDate = formatPickerDate(selectedValues)
  console.log('格式化后的结束日期:', formattedDate)

  // 强制更新响应式数据
  filtrateData.value = {
    ...filtrateData.value,
    end_time: formattedDate
  }

  console.log('更新后的 filtrateData:', filtrateData.value)
  showEndCalendar.value = false
}
// 格式化选择器日期为字符串
const formatPickerDate = (selectedValues) => {
  console.log('格式化选择器日期:', selectedValues)
  if (Array.isArray(selectedValues) && selectedValues.length >= 3) {
    const [year, month, day] = selectedValues
    const result = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
    console.log('格式化结果:', result)
    return result
  }
  return ''
}
// 拖拽开始事件
const onStart = (evt) => {
  console.log('拖拽开始:', evt);
};
// 菜品分类
const onCardClick = (item) => {
  router.push({ path: config.details, query: { id: item.id } });
};
// 拖拽结束事件
const onEnd = (evt) => {
  // 检查事件对象是否存在
  if (!evt) {
    console.error('拖拽事件对象不存在');
    return;
  }

  const oldIndex = evt.oldIndex;
  const newIndex = evt.newIndex;

  // 检查索引是否有效
  if (oldIndex === undefined || newIndex === undefined) {
    console.error('拖拽索引无效:', { oldIndex, newIndex });
    return;
  }

  // 获取当前列表数据
  const currentList = data.list || [];

  // 获取目标记录元素（被拖拽元素原本要插入位置的元素）
  let targetElement = null;

  // 处理特殊情况：移动到列表末尾
  if (newIndex === currentList.length - 1) {
    // 如果移动到最后一个位置，目标元素是最后一个元素的前一个元素
    targetElement = currentList[newIndex - 1];
  }
  // 处理特殊情况：移动到列表开头
  else if (newIndex === 0) {
    // 如果移动到第一个位置，目标元素是第二个元素
    targetElement = currentList[1];
  }
  // 处理一般情况
  else {
    // 一般情况下，目标元素是新位置的下一个元素
    targetElement = currentList[newIndex + 1];
  }

  // // 打印拖拽的id和目标的id
  // console.log('拖拽结束事件:', {
  //   修改排序记录的元素: currentList[newIndex],
  //   目标记录元素: targetElement,
  //   是否从尾端移动到首位: oldIndex === currentList.length - 1 && newIndex === 0,
  //   是否从首位移动到尾端: oldIndex === 0 && newIndex === currentList.length - 1
  // });
  proxy
    .$post(config.curl.sort, {
      drag_row_id: parseInt(currentList[newIndex].id),
      hover_row_id: targetElement ? parseInt(targetElement.id) : null,
      dininghall_id: parseInt(localStorage.getItem('dininghall'))
    })
    .then((res) => {
      if (res.code === 200) {
        showToast(res.msg);
        // onSearch();
      } else {
        throw res.msg;
      }
    })
    .catch((err) => {
      data.error = true;
      showToast(err);
    })
    .finally(() => {
      data.loading = false;
    });
  // 显示排序变更提示
  // showToast('排序已更新');
};
const setRight = () => {
  proxy.$_dd.biz.navigation.setRight({
    text: sort_type.value ? '完成' : '排序',
    control: true,
    onSuccess: function () {
      sort_type.value = !sort_type.value;
      setRight();
    },
    onFail: function () { },
  });
};
if (config.sort === true) {
  setRight()
}
// 刷新列表方法
const refresh = () => {
  initData();
  // 如果启用了骨架屏，显示骨架屏
  if (config.skeleton.isShow) {
    showSkeletonScreen();
  }
  onLoad();
};

defineExpose({ del, refresh });
setTimeout(() => {
  data.initStatus = true;
}, 100);
</script>
<style lang="scss" scoped>
.filter-content-right {
  flex: 1;
  padding-left: 5px;
  background-color: #fff;
  padding-top: 16px;

  // 日期快速选项样式
  .date-quick-options {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;

    .quick-btn {
      min-width: 50px;
      font-size: 14px;
      border-radius: 6px;
      padding: 0 12px;
      height: 32px;
    }
  }

  // 日期范围字段样式
  .date-range-fields {
    // display: flex;
    align-items: center;
    gap: 8px;

    .date-field {
      flex: 1;
      background: #f7f8fa;
      border-radius: 6px;
      font-size: 14px;
      padding: 0 12px;
      height: 36px;
      line-height: 36px;
    }

    .date-sep {
      color: #c8c9cc;
      margin: 0 4px;
      font-size: 14px;
    }
  }

  // 金额范围字段样式
  .amount-range-fields {
    display: flex;
    align-items: center;
    gap: 8px;

    .amount-field {
      flex: 1;
      // background: #f7f8fa;

      font-size: 14px;
      padding: 0 12px;
      padding-left: 8px;
      height: 36px;
      line-height: 36px;
    }

    .amount-sep {
      color: #c8c9cc;
      margin: 0 4px;
      font-size: 14px;
    }
  }

  // 收支类型选项样式
  .income-type-options {
    display: flex;
    gap: 12px;
    margin-top: 16px;

    .income-type-btn {
      min-width: 50px;
      font-size: 13px;
      border-radius: 6px;
      padding: 0 12px;
      height: 36px;
    }
  }

  // 变动类型网格样式
  .change-type-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-bottom: 8px;

    .change-type-btn {
      font-size: 13px;
      border-radius: 6px;
      padding: 6px 8px;
      height: 36px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  // 更多选项样式
  .more-options {
    text-align: right;
    margin-top: 8px;

    .more-btn {
      font-size: 13px;
      border-radius: 6px;
      padding: 4px 12px;
      height: 28px;
      color: #646566;
      border: 1px solid #ebedf0;
    }
  }
}


.dish-card {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  margin: 12px 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  }

  .card-image {
    margin-right: 12px;
    flex-shrink: 0;

    .van-image {
      border-radius: 8px;
      overflow: hidden;
    }
  }

  .asdas {
    text-align: center;
    line-height: 5rem;
    background: #1678ff;
    color: #fff;
    width: 80px;
    height: 80px;
    border-radius: 4px;
  }

  .card-content {
    flex: 1;
    min-width: 0;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .dish-title {
        font-size: 16px;
        font-weight: 600;
        // line-height: 25px;
        color: #323233;
        margin: 0;
        flex: 1;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 8px;
      }

      .dish-tag {
        flex-shrink: 0;
        // background: #1989fa;
        // color: #fff;
        border: none;
        font-size: 12px;
        padding: 2px 8px;
        // border-radius: 12px;
      }

      .dish-A {
        flex-shrink: 0;
        font-size: 14px;
        color: #969799;
      }
    }

    .dish-desc {
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      color: #969799;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .dish-price {
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;
      color: #ff6b35;
    }
  }

  .card-contentA {
    flex: 1;
    min-width: 0;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px;
      // margin-bottom: 8px;

      .dish-title {
        font-size: 16px;
        font-weight: 600;
        // line-height: 25px;
        color: #323233;
        margin: 0;
        flex: 1;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        // margin-right: 8px;
      }

      .dish-tag {
        flex-shrink: 0;
        // background: #1989fa;
        // color: #fff;
        border: none;
        font-size: 12px;
        // padding: 2px 8px;
        // border-radius: 12px;
      }

      .dish-A {
        flex-shrink: 0;
        font-size: 14px;
        color: #969799;
      }
    }

    .dish-desc {
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      color: #969799;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .dish-price {
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;
      color: #ff6b35;
    }
  }
}

.move {
  // 竖行中间位置
  transform: translateY(-27%);
  cursor: move;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    transition: opacity 0.2s ease;
    opacity: 0.6;

    &:hover {
      opacity: 1;
    }
  }
}

// 收支类型选项样式
.income-type-options {
  display: flex;
  gap: 12px;
  margin-top: 16px;

  .income-type-btn {
    min-width: 50px;
    font-size: 14px;
    border-radius: 6px;
    padding: 0 12px;
    height: 32px;
  }
}

// 拖拽时的样式
.sortable-ghost {
  opacity: 0.5;
  background: #f0f0f0;
}

.sortable-chosen {
  background: #e8f4fd;
  border: 1px dashed #1989fa;
}

.sortable-drag {
  opacity: 0.8;
  transform: rotate(2deg);
}

.skeleton-demo-container {
  min-height: 100vh;
  background: #f7f8fa;
}

.add-button-container {
  padding: 16px;
  padding-bottom: 0;

  .add-button {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 8px;

    .add-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: normal;
      line-height: 22px;
      color: #323233;
    }
  }
}

.amount-range-fields {
  display: flex;
  align-items: center;
  gap: 8px;

  .amount-field {
    flex: 1;
    // background: #f7f8fa;

    font-size: 14px;
    padding: 0 12px;
    padding-left: 8px;
    height: 36px;
    line-height: 36px;
  }

  .amount-sep {
    color: #c8c9cc;
    margin: 0 4px;
    font-size: 14px;
  }
}

.demo-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin: 16px;
  background: #fff;
  border-radius: 8px;

  .item-content {
    flex: 1;
    min-width: 0;
    margin-top: 10px;

    /* 确保flex子项能够收缩 */
    .item-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      letter-spacing: normal;
      color: #171A1D;
      margin-bottom: 8px;
    }

    .item-desc {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
    }

    .item-time {
      font-size: 14px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: normal;
      color: #9E9E9E;
    }
  }

  .item-action {
    margin-left: 12px;
  }
}

.wrapper-list-com {
  width: 100%;
  min-height: 100vh;
  border-top: 1px solid #f6f6f6;
  padding-bottom: 60px;

  .van-popup {
    border-top: 1px solid #fff;
  }

  .button-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 12px 16px 16px;
  }

  // 骨架屏样式
  .skeleton-container {
    padding: 16px;

    .skeleton-item {
      margin-bottom: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.filter-popup-actions {
  display: flex;
  position: fixed;
  gap: 12px;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  height: 50px;
  // margin-top: 12px;
  z-index: 1000;
  // padding-bottom: env(safe-area-inset-bottom);

  .van-button {
    flex: 1;
    font-size: 16px;
    border-radius: 20px;
    width: 165px;
    height: 44px;
  }
}
</style>
