<template>
  <div class="wrapper">
    <yhc-form 
    :config="config" 
    pageType="detail"
    :editRedirectConfig="editRedirectConfig"
    @onSubmit="onFormSubmit" />
  </div>
</template>
<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'
import { getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance();
const route = useRoute()

const config = {
  curl: {
    info: "/consumption_rule/get_info",
    del: "/consumption_rule/post_del",
  },
  postData:{
    _isDetailPage: true, // 标识这是详情页面，用于yhc-select-nav判断只读模式
  },

  search: {},

  groupForm: [
    [0, 1],
    [1, 2],
    [2, 3],
    [3, 4],
    [4, 5],
    [5, 7],
    [7, 8],
    [8, 9],
    [9, 10],
    [10, 11],
    [11, 12],
    [12, 13],
    [13, 14],
    [14, 15],
    [15, 17],
  ],
  form: [
    {
      label: "规则名称",
      key: "title",
      required: true,
      component: "yhc-input",
      disabled: true,
      rules: [{ required: true, message: "请填写消费规则名称" }]
    },
    {
      label: "临时方案",
      key: "is_temporary",
      component: "yhc-switch",
      disabled: true,
      rules: [{ required: false, message: "临时方案" }],
      child: {
        showMode: false,
        formChild: [
          {
            label: "失效时间",
            type: "datetime",
            required: true,
            key: "expire_time",
            disabled: true,
            component: "yhc-picker-date",
            rules: [{ required: true, message: "请填写截止时间" }],
          },
        ],
      },
    },
    {
        label:"适用人员",
        key: "apply_personnel_type",
        component: "yhc-radio-group",
        disabled: true,
        rules: [{ required: true, message: "请选择适用人员" }],
        options: [
          { value: 0, label: "全部人员" },
          { value: 1, label: "指定人员" }
        ],
        shape:"dot",
        defaultValue: 0,
        child: {
        showMode: 1, // 当选中"指定人员"时显示子表单
        form: [
          {
            label: "内部员工",
            key: "internal_staffs",
            component: "yhc-select-user",
            disabled: true,
          },
           {
            label: "外部员工",
            key: "external_staffs",
            component: "yhc-select-user",
            disabled: true,
          }
        ]
      }
    },
    {
        label:"适用餐时",
        key: "apply_mealtime_type",
        component: "yhc-radio-group",
        disabled: true,
        rules: [{ required: true, message: "请选择适用餐时" }],
        options: [
          { value: 0, label: "全部餐时" },
          { value: 1, label: "指定餐时" }
        ],
        shape:"dot",
        defaultValue: 0,
        child: {
        showMode: 1, // 当选中"指定餐时"时显示子表单
        form: [
          {
            label: "指定餐时",
            key: "mealtimes",
            component: "yhc-picker",
            disabled: true,
            rules: [{ required: true, message: "请选择餐时" }],
            opts: {
              url: "/mealtime/get_all",
              postData: {},
              merge: false,
              multiple: true,
              text_key: "title",
              contrast_key: "id",
              keyMap: [{ title: "title", id: "id" }],
              defaultList: [],
              card:[
                {
                  title: "title",
                  slotMap: {
                    desc: [
                      {
                        title: "开始时间：",
                        key: "start_time",
                      },
                      {
                        title: "结束时间：",
                        key: "end_time",
                      },
                    ],
                  },
                },
              ]
            },


          },

        ]
      }
    },
    {
        label:"适用档口",
        key: "apply_stall_type",
        component: "yhc-radio-group",
        disabled: true,
        rules: [{ required: true, message: "请选择适用档口" }],
        options: [
          { value: 0, label: "全部档口" },
          { value: 1, label: "指定档口" }
        ],
        shape:"dot",
        defaultValue: 0,
        child: {
        showMode: 1, // 当选中"指定档口"时显示子表单
        form: [
          {
            label: "指定档口",
            key: "stalls",
            component: "yhc-picker",
            disabled: true,
            rules: [{ required: true, message: "请选择档口" }],
            opts: {
              url: "/stall/get_all",
              postData: {},
              merge: false,
              multiple: true,
              text_key: "title",
              contrast_key: "id",
              keyMap: [{ title: "title", id: "id" }],
              defaultList: [],
            },
          },

        ]
      }
    },
     {
      label: "考勤规则",
      key: "attendance_rule",
      component: "yhc-picker",
      disabled: true,
      default: {},
      // rules: [{ required: true, message: "请选择考勤规则" }],
      opts: {
        url: "/attendance_rule/get_all",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "rule_name",
        contrast_key: "id",
        keyMap: { title: "rule_name", id: "id" },
        defaultList: [],
      },
      addButton: {
        show: true,
        text: "添加考勤规则",
        icon: "plus",
        type: "primary",
        routeUrl: "/systemConfig/attendanceRuleAdd",
      },
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false,
      },
      
    },
    {
      label: "减免规则",
      key: "discount_rule",
      component: "yhc-picker",
      disabled: true,
      default:{},
      // rules: [{ required: true, message: "请选择减免规则" }],
      opts: {
        url: "/discount_rule/get_all",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "rule_name",
        contrast_key: "id",
        keyMap: { title: "rule_name", id: "id" },
        defaultList: [],
      },
      addButton:{
        show: true,
        text: "添加减免规则",
        icon: "plus",
        type: "primary",
        routeUrl:"/systemConfig/reduceRuleAdd"
      },
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false,
      },
      card: {
        slotMap: {
          desc: [
            {
              title: "说明：",
              key: "detail",
            },
          ],
        },
        title: "title",
      },
    },
    {
        label:"每餐消费次数",
        key: "consumption_times_per_meal",
        component: "yhc-stepper",
        disabled: true,
        required: true,
        rules: [{ required: false, message: "请填写每餐消费次数" }],
    },
    {
        label:"消费金额上限",
        key: "amount_limit_enabled",
        component: "yhc-radio-group",
        disabled: true,
        rules: [{ required: false, message: "限制消费" }],
        options: [
          { value: 0, label: "无上限" },
          { value: 1, label: "自定义上限" }
        ],
        shape:"dot",
        defaultValue: 0,
        child: {
        showMode: 1, // 当选中"自定义上限"时显示子表单
        form: [
          {
            label: "上限金额",
            key: "amount_limit",
            component: "yhc-input",
            required: true,
            disabled: true,
            type:"number",
            rules: [{ required: true, message: "请输入限额金额" }],
            "right-icon": "元",
            decimalPlaces: true,
            min: 1
          },
          {
            label:"统计范围",
            component: "yhc-radio-group",
            disabled: true,
            key: "amount_limit_scope",
            shape:"dot",
            defaultValue: 0,
            options: [
              {
                label: "当前餐厅",
                value: 0
              },
              {
                label: "全部场所",
                value: 1
              }
            ]
          },
          {
            label:"统计周期",
            component:"yhc-picker",
            defaultValue: 2,
            disabled: true,
            key: "amount_limit_period",
            opts: {
              url: "",
              postData: {},
              merge: false,
              multiple: false,
              text_key: "label",
              contrast_key: "value",
              keyMap: "value",
              defaultList: [
                {
                  label: "每餐",
                  value: 0
                },
                {
                  label: "每天",
                  value: 1
                },
                {
                  label: "每周",
                  value: 2
                },
                {
                  label: "每月",
                  value: 3
                }
              ]
            }
          }
        ]
      }

    },
    {
        label:"单次消费加价",
        key: "single_consumption_markup",
        component: "yhc-input",
        disabled: true,
        required: true,
        labelWidth:110,
        type: "number",
        decimalPlaces: true,
        "right-icon": "元",
        rules: [{ required: false, message: "限制消费" }],
    },
    {
      label:"例：填1元，每次消费加价1元",
      component: "yhc-desc"
    },
    {
        label:"线上预订",
        key:"online",
        component:"yhc-select-nav",
     
        routeUrl:"/consumeRuleOnlineBook",
        routeQuery: {
          id: route.query.id,
          readonly: 'true',
          from: 'detail'
        }
    }
  ]
};

// ==================== 数据处理逻辑 ====================
// 监听表单数据变化，当数据加载完成后进行处理
const onFormSubmit = (formData) => {
  // 这里是表单提交逻辑，detail页面通常不需要提交
  console.log('Detail form data:', formData);
}

// detail页面不再需要数据传递逻辑，onlineBook页面会根据id参数自行获取数据

// 修改按钮跳转配置
const editRedirectConfig = {
  path: '/consumeRuleAdd', // 跳转到消费规则添加页面进行编辑
  query: {
    id: route.query.id, // 传递id参数
    from: 'detail' // 标识来源
  }
  
}

const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title:'消费规则详情',
  });
};
setRightA()
setRight()
</script>
<style lang="scss" scoped>
.wrapper {
  width: 100%;
  min-height: calc(100vh - 1px);
  border-top: 1px solid #f2f3f4;
}
</style>
