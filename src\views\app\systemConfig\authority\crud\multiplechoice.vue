<template>
  <template v-if="props.showData == 'text'">
    <van-field
      v-model="showvalue"
      :label="props.label"
      placeholder="请选择"
      input-align="right"
      is-link
      readonly
      @click="openSelect"
      style="font-size: 16px; padding: 16px;"
    >
    </van-field>
  </template>
  <template v-else-if="props.showData == 'tag'">
    <van-field
      :label="props.label"
      input-align="right"
      readonly
      @click="openSelect"
      style="font-size: 16px; padding: 16px;"
    >
      <template #right-icon>
        <van-icon name="add-o" />
      </template>
    </van-field>
    <div class="select-box" v-if="showvalue">
      <template v-for="(el, i) in checked" :key="i">
        <div class="select-item" @click="delSelect(i)">
          {{ el[props.remote.label] }}
          <van-icon
            name="cross"
            :style="{ color: '#bebec2', marginLeft: '13px' }"
          />
        </div>
      </template>
    </div>
  </template>
  <van-popup
    class="van-popup"
    v-model:show="showBottom"
    position="bottom"
    :style="{ height: '60%' }"
    round
    closeable
    close-icon-position="top-left"
  >
    <div class="header">
      <div class="title">{{ props.label }}</div>
      <div class="ok" @click="offPopup">确定</div>
    </div>
    <div class="selectItmes">
      <template v-if="list">
        <template v-if="props.all_select">
          <van-cell center title="全选">
            <template #right-icon>
              <van-switch v-model="allChecked" @change="allChange" />
            </template>
          </van-cell>
        </template>
        <van-checkbox-group v-model="checked">
          <van-cell-group>
            <van-cell
              v-for="(item, index) in list"
              clickable
              :key="item.id"
              :title="item[props.remote.label]"
              @click="toggle(index)"
            >
              <template #icon>
                <van-checkbox
                  class="selectIcon"
                  :name="item"
                  :ref="(el) => (checkboxRefs[index] = el)"
                  @click.stop
                />
              </template>
              <template #label>{{ item[props.remote.secondLabel] }}</template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </template>
      <template v-else>
        <van-empty
          image="http://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/huashan/2023-07-06/ySOHjSamw6AqowTAw6a6vpPY3TmJnBsa.png"
          image-size="250"
          :description="description"
        />
      </template>
    </div>
  </van-popup>
</template>
    
    <script setup>
import {
  ref,
  reactive,
  onBeforeUpdate,
  onBeforeMount,
  onMounted,
  getCurrentInstance,
  watch,
} from "vue";
import {
  showImagePreview,
  showSuccessToast,
  showFailToast,
  showToast,
} from "vant";
import { useRouter } from "vue-router";

// 接口
const { proxy } = getCurrentInstance();
// emit 发送数据
const emit = defineEmits(["postData"]);

let showvalue = ref();
let allChecked = ref(false);

const props = defineProps({
  label: String,
  is_second: {
    type: Boolean,
    default: false,
  },
  is_remote: {
    type: Boolean,
    default: false,
  },
  remote: Object,
  showData: {
    type: String,
    default: "text",
  },
  all_select: {
    type: Boolean,
    default: false,
  },
  info: String,
});
import { toRefs, defineProps } from 'vue'
//使用父组件传递过来的值
const arr =ref()
arr.value=props.info
console.log(toRefs(props).info._object,props.info);
const showBottom = ref(false);

// 打开遮罩层
const openSelect = () => {
  console.log(props.info,'sdsdsdsd');
  showBottom.value = true;
  // checkboxRefs.value = [];
  // checked.value = props.info;
  console.log( checked.value ,' checked.value ');
};
onBeforeUpdate(() => {
  console.log(checkboxRefs.value,'checkboxRefs.value');
      checkboxRefs.value = [];
    })
const checked = ref([]);
const list = ref();
const checkboxRefs = ref([]);
const toggle = (index) => {
  checkboxRefs.value[index].toggle();
};

onMounted(() => {
  if (props.is_remote) {
    // 远程
    getList();
  }
});

// 空状态描述
let description = ref("暂无数据");

// 获取数据列表
const getList = () => {
  proxy
    .$get(props.remote.api)
    .then((res) => {
      //   console.log(res);
      if (res.errcode == 0) {
        if (res.result.length != 0) {
          list.value = res.result;
        }
      } else {
        description.value = res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 确认按钮
const offPopup = () => {
  showBottom.value = false;
  let value = "";
  // checked.value=  props.info
  console.log("选择数据 --->", checked.value);
  for (let i = 0; i < checked.value.length; i++) {
    let el = checked.value[i];
    if (i == 0) {
      value = value + el[props.remote.label];
    } else {
      value = value + "、" + el[props.remote.label];
    }
  }
  //   console.log("---------------");
  //   console.log(value);
  showvalue.value = value;
  emit("postData", checked.value);
};

// 全选
const allChange = (val) => {
  // console.log("全选按钮状态--->", val);
  if (val) {
    checked.value = JSON.parse(JSON.stringify(list.value));
    console.log('全选信息 -->',checked.value);
  } else {
    checked.value = [];
  }
};

// 删除选择
const delSelect = (i) => {
  checked.value.splice(i, 1);
  emit("postData", checked.value);
};

watch(checked, (val, oldval) => {
  // console.log("监听到checked的值的变化 --->", val);
  if (val.length == list.value.length) {
    allChecked.value = true;
  } else if (val.length == 0) {
    allChecked.value = false;
  }
});
</script>
    
  <style lang="scss" scoped>
// 二级头
.second {
  width: 24px;
  height: 24px;
}

.van-popup {
  .header {
    position: relative;
    width: 100%;
    height: 52px;
    // background-color: blue;
    line-height: 52px;
    border-bottom: 0.5px solid #eaebed;

    .title {
      text-align: center;
    }

    .ok {
      position: absolute;
      right: 16px;
      top: 0;
    }
  }

  .selectItmes {
    .selectIcon {
      margin-right: 20px;
    }
  }
}

.select-box {
  margin: 16px 0;
  width: 90%;
  margin-left: 5%;
  display: flex;
  flex-wrap: wrap;
  .select-item {
    font-size: 14px;
    margin-top: 6px;
    margin-right: 8px;
    padding: 5px 10px;
    border-radius: 4px;
    // border: 1px solid #a8a8a8;
    background-color: #f2f2f6;
  }
}
</style>