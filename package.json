{"name": "vite", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build"}, "dependencies": {"@vuemap/vue-amap": "^2.1.12", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@wecom/jssdk": "^1.4.5", "axios": "^1.4.0", "dayjs": "^1.11.9", "dingtalk-jsapi": "^3.0.33", "echarts": "^6.0.0", "mitt": "^3.0.1", "npm": "^10.4.0", "nprogress": "^0.2.0", "pinia": "^2.0.36", "pinia-plugin-persistedstate": "^3.1.0", "vant": "^4.0.0", "vue": "^3.2.45", "vue-pdf-embed": "^1.1.6", "vue-qr": "^4.0.9", "vue-router": "4", "vue3-pdfjs": "^0.1.6", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^3.2.0", "sass": "^1.62.1", "unplugin-auto-import": "^0.15.3", "unplugin-vue-components": "^0.22.11", "vite": "^3.2.4", "vite-plugin-html": "^3.2.2"}, "license": "ISC"}