<template>
  <div class="wrapper">
    <van-image
      width="100"
      height="100"
      radius="4"
      src="/icons/yunyizhifu.png"
    />
    <van-loading type="spinner" size="20">{{ text }}...</van-loading>
  </div>
</template>
<script setup>
import { ref } from "vue";

import { useLoginStore } from "@/store/dingLogin";
import { showToast} from "vant";
import { parseUrlParams } from "@/untils";

const router = useRouter();
const route = useRoute();
const app = useLoginStore();
let { query } = route;

// 从URL中解析参数
const urlParams = parseUrlParams();
console.log("URL参数解析结果:", urlParams);

let text = ref("登录中");
function handleRouter() {
  text.value = "登录成功";
  setTimeout(() => {
    // console.log("登录成功---state状态-----》",window.history.state);
    if (window.history.state.back) {
      router.go(-1);
    } else {
      router.replace({ path: "/home" });
      //判断产品授权是否再次展示
      // sessionStorage.setItem("is_expire", 0);
    }
  }, 1500);
}

if (app.browserEnv == "wx") {
  app.getWxAuthCode().then((res) => {
    handleRouter();
  });
} else {
  // 设置从URL解析的参数到store中
  if (urlParams.corpid) {
    app.corpId = urlParams.corpid;
  }
  if (urlParams.runtime) {
    app.runtime = urlParams.runtime;
  }
  if (urlParams.appid) {
    app.appid = urlParams.appid;
  }

  app
    .h5Login()
    .then((res) => {
      console.log(res,77788)
      handleRouter();
    })
    .catch((err) => {
      console.log(err);
      showToast(err);
    });
}
</script>
<style lang="scss" scoped>
.wrapper {
  width: 100%;
  min-height: 100vh;
  text-align: center;
  .van-image {
    margin-top: 30%;
  }
  .van-loading {
    margin-top: 20px;
  }
}
</style>
