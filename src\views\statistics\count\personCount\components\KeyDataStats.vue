<template>
  <div class="key-data-stats">
    <!-- 头部区域 -->
    <div class="header-section">
      <!-- 标题行 -->
      <div class="header-top">
        <div class="header-title">关键数据</div>
        <div class="header-right">
          <!-- 日期选择 -->
          <div class="date-selector" @click="showDatePicker = true">
            <span class="date-text">{{ selectedDateText }}</span>
            <van-icon name="notes-o" size="16" color="#C8C9CC" />
          </div>

          <!-- 筛选按钮 -->
          <div class="filter-selector" @click="openFilterPopup">
            <span class="filter-text">筛选</span>
            <van-icon name="play" size="12" color="#C8C9CC" style="transform: rotate(90deg);" />
          </div>
        </div>
      </div>

      <!-- 已选择筛选条件 -->
      <div class="filter-tags" v-if="hasActiveFilters">
        <span>已选择：</span>
        <span>{{ activeFiltersText }}</span>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-grid">
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-label">已消费人次</div>
          <div class="stats-value">{{ formatNumber(statsData.consumed_count) }}</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-label">已预定人次</div>
          <div class="stats-value">{{ formatNumber(statsData.reserved_count) }}</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-label">预定核销人次</div>
          <div class="stats-value">{{ formatNumber(statsData.reserved_verified_count) }}</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-label">预定未核销人次</div>
          <div class="stats-value">{{ formatNumber(statsData.reserved_unverified_count) }}</div>
        </div>
      </div>
    </div>

    <!-- 日期选择弹窗 -->
    <van-popup v-model:show="showDatePicker" round position="bottom">
      <van-date-picker
        v-model="currentDate"
        title="请选择时间"
        :columns-type="['month', 'day']"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
        :show-toolbar="true"
      />
    </van-popup>

    <!-- 筛选弹窗 -->
    <van-popup
      v-model:show="showFilterPopup"
      round
      position="bottom"
      :style="{ height: '70vh' }"
    >
      <div class="filter-popup">
        <!-- {{ AURA-X: Modify - 重新设计为表单样式的筛选弹窗 }} -->
        <div class="filter-header">
          <span class="filter-title">筛选条件</span>
        </div>

        <!-- {{ AURA-X: Add - 添加左右分栏布局 }} -->
        <div class="filter-body">
          <!-- 左侧导航菜单 -->
          <div class="filter-nav">
            <div
              class="filter-nav-item"
              :class="{
                active: activeNavItem === 'dininghall',
                'has-selection': selectedFilters.dininghall !== null
              }"
              @click="scrollToSection('dininghall')"
            >
              <span class="nav-indicator" :class="{ 'has-data': selectedFilters.dininghall !== null }"></span>
              <span class="nav-text">场所</span>
            </div>
            <div
              v-if="mealtimeOptions.length > 0"
              class="filter-nav-item"
              :class="{
                active: activeNavItem === 'mealtime',
                'has-selection': selectedFilters.mealtime.length > 0
              }"
              @click="scrollToSection('mealtime')"
            >
              <span class="nav-indicator" :class="{ 'has-data': selectedFilters.mealtime.length > 0 }"></span>
              <span class="nav-text">餐时</span>
            </div>
            <div
              v-if="stallOptions.length > 0"
              class="filter-nav-item"
              :class="{
                active: activeNavItem === 'stall',
                'has-selection': selectedFilters.stall.length > 0
              }"
              @click="scrollToSection('stall')"
            >
              <span class="nav-indicator" :class="{ 'has-data': selectedFilters.stall.length > 0 }"></span>
              <span class="nav-text">档口</span>
            </div>
          </div>

          <!-- 右侧筛选内容 -->
          <div class="filter-content" ref="filterContentRef">
            <!-- 场所筛选 -->
            <div class="filter-form-item" ref="dininghallRef">
              <div class="filter-label">
                <span class="label-text">场所</span>
              </div>
              <div class="filter-value">
                <div class="filter-select-options">
                  <div
                    v-for="dininghall in dininghallOptions"
                    :key="dininghall.id"
                    class="filter-select-option"
                    :class="{ active: selectedFilters.dininghall === dininghall.id }"
                    @click="selectFilter('dininghall', dininghall.id)"
                  >
                    {{ dininghall.title }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 餐时筛选 -->
            <div class="filter-form-item" v-if="mealtimeOptions.length > 0" ref="mealtimeRef">
              <div class="filter-label">
                <span class="label-text">餐时</span>
              </div>
              <div class="filter-value">
                <div class="filter-select-options">
                  <div
                    v-for="mealtime in mealtimeOptions"
                    :key="mealtime.id"
                    class="filter-select-option"
                    :class="{ active: selectedFilters.mealtime.includes(mealtime.id) }"
                    @click="selectFilter('mealtime', mealtime.id)"
                  >
                    {{ mealtime.title }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 档口筛选 -->
            <div class="filter-form-item" v-if="stallOptions.length > 0" ref="stallRef">
              <div class="filter-label">
                <span class="label-text">档口</span>
              </div>
              <div class="filter-value">
                <div class="filter-select-options">
                  <div
                    v-for="stall in stallOptions"
                    :key="stall.id"
                    class="filter-select-option"
                    :class="{ active: selectedFilters.stall.includes(stall.id) }"
                    @click="selectFilter('stall', stall.id)"
                  >
                    {{ stall.title }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- {{ AURA-X: Modify - 重新设计底部按钮样式 }} -->
        <div class="filter-footer">
          <div class="filter-button reset-button" @click="onFilterReset">
            重置
          </div>
          <div class="filter-button confirm-button" @click="onFilterConfirm">
            确定
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue'
import { showToast } from 'vant'

const { proxy } = getCurrentInstance()

// 响应式数据
const showDatePicker = ref(false)
const showFilterPopup = ref(false)
const isRefreshing = ref(false)

// {{ AURA-X: Add - 左侧导航相关数据 }}
const activeNavItem = ref('dininghall')
const filterContentRef = ref(null)
const dininghallRef = ref(null)
const mealtimeRef = ref(null)
const stallRef = ref(null)

// {{ AURA-X: Modify - 使用Vant DatePicker的月日数据，只包含月和日 }}
const currentDate = ref(['01', '01']) // DatePicker只需要月和日
const selectedDateText = ref('')

// {{ AURA-X: Modify - 初始化当天月日 }}
const initTodayDate = () => {
  const today = new Date()
  const month = (today.getMonth() + 1).toString().padStart(2, '0')
  const day = today.getDate().toString().padStart(2, '0')

  currentDate.value = [month, day]
  selectedDateText.value = formatDateDisplay([month, day])
}

// 统计数据
const statsData = reactive({
  consumed_count: 1230,
  reserved_count: 1230,
  reserved_verified_count: 1230,
  reserved_unverified_count: 1230,
  total_amount: 15680.50,
  active_stalls: 12,
  active_users: 856,
  average_rating: 4.8,
  refund_orders: 23,
  coupon_used: 145,
  popular_dishes: 8,
  abnormal_orders: 5
})

// 筛选相关数据
const selectedFilters = reactive({
  dininghall: null,     // 场所ID
  mealtime: [],         // 餐时ID数组（多选）
  stall: []             // 档口ID数组（多选）
})

// {{ AURA-X: Add - 添加确认后的筛选状态，用于控制页面显示 }}
const confirmedFilters = reactive({
  dininghall: null,     // 确认后的场所ID
  mealtime: [],         // 确认后的餐时ID数组
  stall: []             // 确认后的档口ID数组
})

// 筛选选项数据
const dininghallOptions = ref([])  // 场所选项
const mealtimeOptions = ref([])    // 餐时选项
const stallOptions = ref([])       // 档口选项

// 计算属性
const hasActiveFilters = computed(() => {
  // {{ AURA-X: Modify - 只有确认后的筛选条件才显示在页面上 }}
  return confirmedFilters.dininghall !== null ||
         (confirmedFilters.mealtime && confirmedFilters.mealtime.length > 0) ||
         (confirmedFilters.stall && confirmedFilters.stall.length > 0)
})

const activeFiltersText = computed(() => {
  const filters = []

  // 场所筛选 - 显示确认后的餐厅名称
  if (confirmedFilters.dininghall) {
    const dininghall = dininghallOptions.value.find(d => d.id === confirmedFilters.dininghall)
    if (dininghall) filters.push(dininghall.title)
  } else if (confirmedFilters.dininghall === null && (confirmedFilters.mealtime.length > 0 || confirmedFilters.stall.length > 0)) {
    // {{ AURA-X: Modify - 只有在有其他筛选条件时才显示"全部场所" }}
    // filters.push('全部场所')
  }

  // 餐时筛选（多选）
  if (confirmedFilters.mealtime && confirmedFilters.mealtime.length > 0) {
    const mealtimeNames = confirmedFilters.mealtime.map(id => {
      const mealtime = mealtimeOptions.value.find(m => m.id === id)
      return mealtime ? mealtime.title : ''
    }).filter(name => name)
    if (mealtimeNames.length > 0) {
      filters.push(`餐时：${mealtimeNames.join('、')}`)
    }
  }

  // 档口筛选（多选）
  if (confirmedFilters.stall && confirmedFilters.stall.length > 0) {
    const stallNames = confirmedFilters.stall.map(id => {
      const stall = stallOptions.value.find(s => s.id === id)
      return stall ? stall.title : ''
    }).filter(name => name)
    if (stallNames.length > 0) {
      filters.push(`档口：${stallNames.join('、')}`)
    }
  }

  return filters.join('；')
})

// 方法
// {{ AURA-X: Modify - 格式化月日显示，去掉前导零 }}
const formatDateDisplay = (dateArray) => {
  if (!dateArray || dateArray.length < 2) return ''
  const [month, day] = dateArray
  return `${parseInt(month)}月${parseInt(day)}日`
}

// {{ AURA-X: Modify - 日期确认回调，处理不同的数据格式 }}
const onDateConfirm = (value) => {
  console.log('DatePicker confirm value:', value) // 调试用

  // 处理不同的数据格式
  let dateArray = []
  if (Array.isArray(value)) {
    dateArray = value
  } else if (value && typeof value === 'object') {
    // 如果是对象格式，尝试提取值
    if (value.selectedValues) {
      dateArray = value.selectedValues
    } else if (value.values) {
      dateArray = value.values
    }
  }

  if (dateArray.length >= 2) {
    currentDate.value = dateArray
    selectedDateText.value = formatDateDisplay(dateArray)
    showDatePicker.value = false
    // 重新加载统计数据
    loadStatisticsData()
  } else {
    console.error('Invalid date format:', value)
  }
}

// {{ AURA-X: Modify - 筛选选择方法，支持单选和多选 }}
const selectFilter = (type, value) => {
  if (type === 'dininghall') {
    // 场所单选
    selectedFilters[type] = value
    // 场所变更时，清空餐时和档口选择，并重新加载数据
    selectedFilters.mealtime = []
    selectedFilters.stall = []
    if (value) {
      loadMealtimeOptions(value)
      loadStallOptions(value)
    } else {
      mealtimeOptions.value = []
      stallOptions.value = []
    }
  } else if (type === 'mealtime' || type === 'stall') {
    // 餐时和档口多选
    const currentSelection = selectedFilters[type]
    const index = currentSelection.indexOf(value)
    if (index > -1) {
      // 如果已选中，则取消选择
      currentSelection.splice(index, 1)
    } else {
      // 如果未选中，则添加选择
      currentSelection.push(value)
    }
  }
}

// {{ AURA-X: Add - 打开筛选弹窗时重置选择状态为确认状态 }}
const openFilterPopup = () => {
  // 将确认状态复制到选择状态，确保弹窗显示上次确认的选择
  selectedFilters.dininghall = confirmedFilters.dininghall
  selectedFilters.mealtime = [...confirmedFilters.mealtime]
  selectedFilters.stall = [...confirmedFilters.stall]

  // 重置导航状态
  activeNavItem.value = 'dininghall'

  showFilterPopup.value = true
}

// {{ AURA-X: Add - 滚动到指定区域的方法 }}
const scrollToSection = (sectionName) => {
  activeNavItem.value = sectionName

  // 获取对应的ref元素
  let targetRef = null
  switch (sectionName) {
    case 'dininghall':
      targetRef = dininghallRef.value
      break
    case 'mealtime':
      targetRef = mealtimeRef.value
      break
    case 'stall':
      targetRef = stallRef.value
      break
  }

  // 滚动到目标元素
  if (targetRef && filterContentRef.value) {
    const container = filterContentRef.value
    const target = targetRef

    // 计算目标元素相对于容器的位置
    const containerRect = container.getBoundingClientRect()
    const targetRect = target.getBoundingClientRect()
    const scrollTop = container.scrollTop

    // 计算需要滚动的距离
    const targetPosition = targetRect.top - containerRect.top + scrollTop - 20 // 20px的偏移量

    // 平滑滚动
    container.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    })
  }
}

// {{ AURA-X: Modify - 重置筛选条件，但保留场所的默认选择 }}
const onFilterReset = () => {
  // 获取默认的场所ID（从localStorage缓存）
  const cachedDininghallId = localStorage.getItem('dininghall')
  let defaultDininghallId = null

  if (cachedDininghallId) {
    const cachedId = parseInt(cachedDininghallId)
    const foundDininghall = dininghallOptions.value.find(d => d.id === cachedId)
    if (foundDininghall) {
      defaultDininghallId = cachedId
    }
  }

  // 重置筛选条件，但保留默认场所选择
  selectedFilters.dininghall = defaultDininghallId
  selectedFilters.mealtime = []
  selectedFilters.stall = []

  // 如果有默认场所，重新加载对应的餐时和档口数据
  if (defaultDininghallId) {
    loadMealtimeOptions(defaultDininghallId)
    loadStallOptions(defaultDininghallId)
  } else {
    // 如果没有默认场所，清空餐时和档口选项
    mealtimeOptions.value = []
    stallOptions.value = []
  }
}

const onFilterConfirm = () => {
  // {{ AURA-X: Modify - 确认时将选择的筛选条件复制到确认状态 }}
  confirmedFilters.dininghall = selectedFilters.dininghall
  confirmedFilters.mealtime = [...selectedFilters.mealtime]
  confirmedFilters.stall = [...selectedFilters.stall]

  showFilterPopup.value = false
  // 重新加载统计数据
  loadStatisticsData()
}

// 刷新数据
const refreshData = async () => {
  if (isRefreshing.value) return

  isRefreshing.value = true
  try {
    await loadStatisticsData()
    showToast('数据已刷新')
  } catch (error) {
    showToast('刷新失败')
  } finally {
    setTimeout(() => {
      isRefreshing.value = false
    }, 1000)
  }
}

// 格式化数字显示
const formatNumber = (num) => {
  // {{ AURA-X: Modify - 改为千位分隔符格式，如 1,230 }}
  if (num == null || num === undefined) return '0'
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 格式化金额显示
const formatAmount = (amount) => {
  return parseFloat(amount).toFixed(2)
}

// 加载统计数据
const loadStatisticsData = async () => {
  try {
    // {{ AURA-X: Modify - 将DatePicker的月日格式转换为API需要的日期格式 }}
    let dateParam = ''
    if (currentDate.value && currentDate.value.length >= 2) {
      const [month, day] = currentDate.value
      const currentYear = new Date().getFullYear() // 使用当前年份
      dateParam = `${currentYear}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
    }

    // {{ AURA-X: Modify - 使用确认后的筛选条件进行数据查询 }}
    const params = {
      date: dateParam,
      dininghall_id: confirmedFilters.dininghall,
      mealtime_ids: confirmedFilters.mealtime.length > 0 ? confirmedFilters.mealtime.join(',') : '',
      stall_ids: confirmedFilters.stall.length > 0 ? confirmedFilters.stall.join(',') : ''
    }

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })

    const response = await proxy.$post('statistics/key_data', params)

    if (!response.errcode) {
      Object.assign(statsData, response.result)
    } else {
      showToast(response.errmsg || '获取统计数据失败')
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    showToast('获取统计数据失败')
  }
}

//- 加载场所数据 
const loadDininghallOptions = async () => {
  try {
    const response = await proxy.$get('/dininghall/get_all', { type: 0 })
    if (response.code === 200) {
      dininghallOptions.value = response.data.map(item => ({
        id: item.id,
        title: item.title
      }))

      // {{ AURA-X: Modify - 设置默认选择本地缓存的dininghall_id，但不自动确认 }}
      const cachedDininghallId = localStorage.getItem('dininghall')
      if (cachedDininghallId) {
        const cachedId = parseInt(cachedDininghallId)
        const foundDininghall = dininghallOptions.value.find(d => d.id === cachedId)
        console.log('找到的餐厅:', foundDininghall) // 调试日志
        if (foundDininghall) {
          // {{ AURA-X: Modify - 设置筛选弹窗的默认选择，同时设置确认状态显示在页面上 }}
          selectedFilters.dininghall = cachedId
          confirmedFilters.dininghall = cachedId
          console.log('设置默认餐厅ID:', cachedId) // 调试日志
          // 加载对应的餐时和档口数据
          loadMealtimeOptions(cachedId)
          loadStallOptions(cachedId)
        }
      }
    }
  } catch (error) {
    console.error('加载场所数据失败:', error)
  }
}

// {{ AURA-X: Add - 加载餐时数据 }}
const loadMealtimeOptions = async (dininghallId) => {
  try {
    const response = await proxy.$get('/mealtime/get_all', { dininghall_id: dininghallId })
    if (response.code === 200) {
      mealtimeOptions.value = response.data.map(item => ({
        id: item.id,
        title: item.title
      }))
    }
  } catch (error) {
    console.error('加载餐时数据失败:', error)
    mealtimeOptions.value = []
  }
}

// {{ AURA-X: Add - 加载档口数据 }}
const loadStallOptions = async (dininghallId) => {
  try {
    const response = await proxy.$get('/stall/get_all', { dininghall_id: dininghallId })
    if (response.code === 200) {
      stallOptions.value = response.data.map(item => ({
        id: item.id,
        title: item.title
      }))
    }
  } catch (error) {
    console.error('加载档口数据失败:', error)
    stallOptions.value = []
  }
}

// 初始化
onMounted(() => {
  // {{ AURA-X: Modify - 初始化当天日期并加载数据 }}
  initTodayDate()

  // 加载数据
  loadDininghallOptions()  // 先加载场所数据
  loadStatisticsData()
})
</script>

<style lang="scss" scoped>
.key-data-stats {
  margin: 16px;
  background: #Fff;
  border-radius: 8px;
  padding: 16px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.header-section {
  margin-bottom: 16px;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.header-title {
  font-size: 15px;
  font-weight: 500;
  line-height: 21px;
  color: #323233;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-selector,
.filter-selector {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;

}

.date-text,.filter-text{
  font-size: 14px;
  color: #1989FA;
  line-height: 20px;
}

.filter-tags {
  padding: 8px;
  background: #F7F8FA;
  border-radius: 8px;
  font-size: 12px;
  color: #969799;
  line-height: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  // margin-bottom: 20px;
}

.stats-card {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 16px;

}

.stats-content {
  // 无需额外padding
}

.stats-label {
  font-size: 14px;
  color: #323233;
  margin-bottom: 4px;
  line-height: 20px;
  position: relative;
  padding-left: 8px;

  // {{ AURA-X: Modify - 在标签文字前添加蓝色装饰条 }}
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 12px;
    background: #1989fa;
    border-radius: 2px;
  }
}

.stats-value {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  line-height: 22px;
}

// {{ AURA-X: Modify - 重新设计筛选弹窗样式，参考图片的表单样式 }}
.filter-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f7f8fa;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #ebedf0;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

/* {{ AURA-X: Add - 左右分栏布局 }} */
.filter-body {
  z-index: 1000;
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* {{ AURA-X: Add - 左侧导航样式 }} */
.filter-nav {
  width: 80px;
  background: #f2f3f5;
  overflow-y: auto;
}

/* {{ AURA-X: Modify - 重新设计导航项样式，支持红色指示点和完整白色背景 }} */
.filter-nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  line-height: 19px;
  cursor: pointer;
  font-size: 13px;
  color: #323233;
  &.active {
    background: #fff;

    .nav-text {
      color: #323233;
    }
  }

}

/* {{ AURA-X: Add - 导航指示器样式，支持红色数据指示 }} */
.nav-indicator {
  position: absolute;
  top: 26px;
  left: 19px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: transparent;
  transition: all 0.2s;

  &.has-data {
    background: #ff4444;
  }
}

.nav-text {
  font-size: 13px;
  color: #646566;
  text-align: center;
  line-height: 19px;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
  background: #fff;
}

/* {{ AURA-X: Add - 表单项样式，类似图片中的设计 }} */
.filter-form-item {
  background: #fff;
  overflow: hidden;
}

.filter-label {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #fff;
}

.label-text {
  font-size: 14px;
  line-height: 20px;
  color: #323233;
}

.filter-value {
  padding: 0 16px;
  background: #fff;
}

.filter-select-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-select-option {
  padding: 6px 12px;
  background: #F2F3F5;
  border-radius: 8px;
  font-size: 13px;
  line-height: 20px;
  color: #323233;
  cursor: pointer;
  min-width: 82px;
  text-align: center;

  &.active {
    background: rgba(25, 137, 250, 0.1);
    color: #1989FA;
  }
}

/* {{ AURA-X: Modify - 重新设计底部按钮样式 }} */
.filter-footer {
  display: flex;
  padding: 5px 16px;
  padding-bottom: 16px;
  gap: 12px;
  background: #fff;
}

.filter-button {
  flex: 1;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 22px;
  font-size: 16px;
}

.reset-button {
  color: #323233;
  border: 0.5px solid #DCDEE0;
}

.confirm-button {
  background: #1989fa;
  color: #fff;
}
</style>
