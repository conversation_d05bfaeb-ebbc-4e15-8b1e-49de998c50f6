<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form :config="basicFormConfig" pageType="detail" :editRedirectConfig="editRedirectConfig"
      @onSubmit="onBasicSubmit" />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const { proxy } = getCurrentInstance();
const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    id: route.query.id // 从路由获取id参数
  },
  curl: {
    info: '/mealtime_group/get_info', // 获取详情接口
    del: '/mealtime_group/post_del' // 删除接口
  },
  groupForm: [
    [0, 1],
    [1, 4]
  ],
  form: [
    {
      label: "餐时组",
      key: "title",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入",
      required: true,
      // 不允许输入
      disabled: true,
      rules: [{ required: true, message: "请填写餐时组名称" }],
    },
    {
      label: "关联餐时",
      key: "repasts",
      component: "yhc-picker",
      placeholder: "请选择",
      // 不允许输入
      disabled: true,
      required: true,
      rules: [{ required: true, message: "请选择关联餐时" }],
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false
      },
      opts: {
        url: "/mealtime/get_all",
        postData: {},
        merge: false,
        multiple: true,
        maxlength: 14,
        text_key: "title",
        contrast_key: "id",
         keyMap: [{id:"id",title:"title"}],
      },
    }
  ]
}

// 修改按钮跳转配置
const editRedirectConfig = {
  path: '/menuTimeGroupAdd', // 跳转到新增页面进行编辑
  query: {
    id: route.query.id, // 传递id参数
    from: 'detail' // 标识来源
  }
}

// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  showToast('基础表单提交成功')
}
const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: '餐时组详情',
  });
};
setRightA()
setRight()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
