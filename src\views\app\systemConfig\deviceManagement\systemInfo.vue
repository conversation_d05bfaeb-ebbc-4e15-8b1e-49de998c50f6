<template>
    <div class="device-detail-container">
        <div style="display: flex; justify-content: center; align-items: center; flex-direction: column;">
            <img src="https://fastly.jsdelivr.net/npm/@vant/assets/apple-1.jpeg" alt="" style="width:97px;height: 97px;padding: 16px 0;">
            <div style="font-size: 15px;color: #9E9E9E;">
                <p style="line-height: 21px;margin:8px 0;">软件版本：{{ form.software_version }}</p>
                <p style="line-height: 21px;margin: 0;">固件版本：{{ form.firmware_version }}</p>
            </div>
        </div>
        <van-cell-group inset class="device-name-card" style="height: 78px;">
           <div style="padding: 16px 16px;">发现新版本</div>
           <div style="font-size: 14px; color: #969799;padding-left: 16px;">
            <span>软件版本：10.22</span>
            <span style="margin-left: 8px;">固件版本：11.02</span>
           </div>
        </van-cell-group>
        <van-cell-group inset class="device-name-card">
            <van-field v-model="form.SN" label="SN"  input-align="right" style="height: 56px;align-items: center;" :readonly="true" />
        </van-cell-group>
        <div
            style="height: 90px;width: 100%;background-color: #fff;padding: 0 16px;display: flex;justify-content: space-between;align-items: center;position: fixed;bottom: 0px;">
            <van-button square type="primary" style="width: 100%;height: 44px;border-radius: 8px;" v-if="form.status==1"
                @click="confirmSelect">升级固件</van-button>
            <van-button square type="primary" style="width: 100%;height: 44px;border-radius: 8px;" v-else disabled
                >设备离线，暂不可升级</van-button>
        </div>
    </div>
</template>
<script setup>
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();
const form = ref({
    SN: '',
    firmware_version: "",
    software_version: "",
    status: 0
});

onMounted(() => {
    form.value.SN = route.query.sn;
    form.value.firmware_version = route.query.firmware_version;
    form.value.software_version = route.query.software_version;
    form.value.status = route.query.status;
});

const confirmSelect = () => {
    // 升级固件逻辑
    console.log('升级固件');
};
</script>
<style lang="scss" scoped>
.device-detail-container {
    min-height: 90vh;
    background: #f7f8fa;
}
.device-name-card{
    margin-top: 16px;
}

</style>