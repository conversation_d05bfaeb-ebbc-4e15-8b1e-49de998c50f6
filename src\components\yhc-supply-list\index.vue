<template>
  <div class="wrapper-home">
    <div class="top-block">
      <div class="dininghall-date">
        &nbsp;&nbsp;
        <img
            src="https://files.qixuw.com/dingb9614df94342f570a1320dcb25e91351/canyin/2025-07-30/js2ZTijSQ3Q9f1yZZZnsXS6Lio8jQdH0.svg"
            alt="">
        <van-dropdown-menu>
          <van-dropdown-item v-model="curDininghall" :options="option" @change="dingdingChange"/>
        </van-dropdown-menu>
      </div>
      <van-row>
        <van-col :class="`date-block ${i === curDateIndex ? 'cur-date' : ''}`" v-for="(item, i) in dateLs" :key="i"
                 @click="onDateClick(i)">
          <div style="margin-bottom: 5px">{{ item.date }}</div>
          <div>{{ item.week }}</div>
        </van-col>
        <van-col class="date-block date">
          <van-popover v-model:show="showPopover" placement="bottom-end" overlay>
            <template #reference>
              <van-icon name="calendar-o" size="24" style="margin-bottom: 5px"/>
              <div>日历</div>
            </template>
            <van-calendar title="日历" :default-date="new Date(nowDate)" :poppable="false" :show-confirm="true"
                          :style="{ height: '460px', width: '310px' }" row-height="40" :formatter="formatter"
                          @monthShow="monthShow"
                          @select="onDateSelect" @confirm="showPopover = false" ref="calendar"/>
          </van-popover>
        </van-col>
      </van-row>
    </div>
    <!-- <div v-if="isShow">
      <van-notice-bar left-icon="volume-o" :scrollable="true" mode="closeable" color="#007FFF" background="#E0EDFE">
        <span v-for="(item, i) in list" :key="i" @click="onNoticeClick(item)">
          {{ item.text }}&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
        </span>
      </van-notice-bar>
    </div> -->
    <div class="window-info">
      <div class="left">
        <div :class="`repast-time ${i === curMealtimeIndex ? 'active-repast' : ''
          }`" v-for="(item, i) in windowData" :key="i" @click="onRepastClick(i)">
          <!-- <div class="select-line" v-show="i === curMealtimeIndex"></div> -->
          <div class="text-info">
            <!-- <div class="icon">
              <van-icon name="qr" class="van-haptics-feedback" size="24" />
            </div> -->
            <div class="repast-text">{{ item.title }}</div>
          </div>
        </div>
      </div>
      <div class="right" v-if="windowData[curMealtimeIndex]">
        <div class="window-dish" v-for="(item, i) in windowData[curMealtimeIndex].stalls" :key="item.id + i">
          <div class="title-block">
            <div style="width: 80%">
              <span style="color: rgba(23, 26, 29, 0.6);font-size: 12px;">{{ item.title + " " }}</span>
              <span style="white-space: nowrap;color: rgba(23, 26, 29, 0.6);font-size: 12px;">{{
                  item.repast_start_time
                }}-{{ item.repast_end_time }}</span>
              <span style="color: #ff9200">（{{ windowData[curMealtimeIndex].timeOutText }}）</span>
            </div>
            <span v-if="item.price" style="color: #ff9200;font-size: 10px;">￥{{ item.price.toFixed(2) }}</span>
          </div>
          <div class="dish-block" @click="onDishClick(item)" v-if="item.dishes.length">
            <div class="dish-item" v-for="(dish, i) in item.dishes" :key="'dish' + i">
              <van-image v-if="dish.image" width="50" height="50" radius="4" :src="dish.image ||
                'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
                "/>
              <div v-else style="
                  width: 50px;
                  height: 50px;
                  border-radius: 4px;
                  background: #007fff;
                  color: #fff;
                  font-size: 25px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-bottom: 5px;
                ">
                {{ dish.title[0] }}
              </div>
              <div class="title">{{ dish.title }}</div>
            </div>
          </div>
          <div class="dish-block" v-else>
            <div class="dish-item">
              <div style="
                  width: 50px;
                  height: 50px;
                  border-radius: 4px;
                  background: #007fff;
                  color: #fff;
                  font-size: 25px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-bottom: 5px;
                ">
                {{ windowData[curMealtimeIndex].title[0] }}
              </div>
              <div class="title">
                {{ windowData[curMealtimeIndex].title }}
              </div>
            </div>
          </div>
          <div class="bottom-block">
            <div>
              <span v-if="item.apply_margin">剩余:{{ item.apply_margin || 0 }}份</span>
              <span v-if="item.apply_end_time">报餐结束：{{ item.apply_end_time }}</span>
            </div>
            <div>
              <van-button :disabled="!item.can_booking" :plain="!!item.apply_status" hairline type="primary"
                          size="small" :loading="item.loading"
                          @click="onReportClick(item)" style="width: 51px;">预定
              </van-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <van-dialog v-model:show="isShowInput" title="报餐数量" show-cancel-button @confirm="onInpuConfirm">
      <div style="margin: 16px; display: flex; align-items: center">
        <van-field v-model="reportNumber" label="" placeholder="请输入报餐数量">
          <template #button> /份</template>
        </van-field>
      </div>
    </van-dialog>
  </div>
</template>
<script setup>
import {ref, getCurrentInstance} from "vue";
import {showNotify, showToast, closeNotify, ActionSheet} from "vant";
import dayjs from "dayjs";
import {useLoginStore} from "@/store/dingLogin";
import {routerList, set_permission} from "@/views/home/<USER>/routerList";

const router = useRouter();
const app = useLoginStore();
app.item = null;
// console.log("全局数据-------》", app);
let {proxy} = getCurrentInstance();
const curDininghall = ref(0);
let curDateIndex = ref(0);
let curMealtimeIndex = ref(0);
let nowDate = ref("");
let active = ref("");
let windowData = ref([]);
// let reportOrOrder =
//   app.homeCofig && app.homeCofig.reportOrOrder
//     ? ref(app.homeCofig.reportOrOrder)
//     : ref(0);
const showPopover = ref(false);
// const dishShow = ref(false);
// const dishDetail = ref({});
let option = [];
let config = {
  curl: {
    ls: "dininghall/get_all",
    window: "dine/home_info",
    dingdingChange: "dininghall/post_toggle",
  },
  postData: {},
};
const onDateClick = (index) => {
  curDateIndex.value = index;
  nowDate.value = dateLs[index].dayjs.format("YYYY-MM-DD");
  getWindow();
};
const onDishClick = (item) => {
  app.orderDishSelectList = undefined;
  let dishes = item.dishes;
  let obj = {};
  dishes.forEach((dish) => {
    if (obj[dish.category_title]) {
      obj[dish.category_title].push(dish);
    } else {
      obj[dish.category_title] = [dish];
      !active.value && (active = dish.category_title);
    }
  });
  app.indexDishDetail = obj;
  router.push({
    path: "/orderDishDetail",
  });
};
const monthShow = (date) => {
  // console.log("monthShow--------------->", date,nowDate);
};
const formatter = (day) => {
  // console.log("day数据-------》",day)
  return day;
};
let report_data = null;
let postUrl = "";
let isShowInput = ref(false);
let reportNumber = ref(null);
let curItem = ref(null);
const onInpuConfirm = (e) => {
  report_data.count = reportNumber.value;
  if (report_data.advanced) {
    app.report_data = report_data;
    report_data.repast_title = windowData.value[curMealtimeIndex.value].repast_title;
    app.select_order_list = report_data.dishes;
    // showToast(`高级报餐未开放`);
    router.push({
      name: "reportSenior",
      // url: "/reportSenior",
      // params: report_data,
    });
  } else {
    post_reapst(postUrl, report_data);
  }
};
const onReportClick = (item) => {
  if (item.canOrder === 0) {    // 报餐
    // 餐厅名称
    let dininghall_title = option.find(item => item.id == curDininghall.value).title;
    item.dininghall_title = dininghall_title;
    item.date = nowDate.value;
    // 获取选中的餐时
    item.window_title = windowData.value[curMealtimeIndex.value].title
    app.item = item;
    router.push({
      path: "/report/confirm",
    });
  } else {
      // 获取选中餐时的id
      item.repast_id = windowData.value[curMealtimeIndex.value].id;
      let dininghall_title = option.find(item => item.id == curDininghall.value).title;
      item.dininghall_title = dininghall_title;
      //点餐
      item.date = nowDate.value;
      app.orderDishSelectList = undefined;
      app.item = item;
      router.push({
        path: "/orderMeal",
        query: item,
      });
    // showNotify("点餐");
  }
  // console.log("报餐点餐-------》", item);
  // curItem.value = item;
  // if (reportOrOrder.value) {
  //   //点餐
  //   item.date = nowDate.value;
  //   app.orderDishSelectList = undefined;
  //   app.item = item;
  //   router.push({
  //     path: "/orderMeal",
  //     query: item,
  //   });
  // } else {
  //   //报餐
  //   report_data = {
  //     repast_id: item.repast_id,
  //     window_id: item.id,
  //     repast_title: item.repast_title,
  //     window_title: item.title,
  //     date: nowDate.value,
  //   };

  //   if (!item.apply_status) {
  //     // console.log("报餐------》", !item.apply_status);
  //     postUrl = "apply/post_apply_add";
  //     if (item.advanced || item.dept || item.multi) {
  //       report_data.dishes = item.dishes;
  //       report_data.advanced = item.advanced;
  //       item.date = nowDate.value;
  //       app.item = item;
  //       router.push({
  //         path: "/report/confirm",
  //       });
  //     } else {
  //       post_reapst(postUrl, report_data);
  //     }
  //     // if (item.multi) {
  //     //   proxy.$_dd.device.notification.actionSheet({
  //     //     title: "报餐", //标题
  //     //     cancelButton: "取消", //取消按钮文本
  //     //     otherButtons: ["单人报餐", "多人报餐"],
  //     //     onSuccess: (result) => {
  //     //       if (result.buttonIndex === -1) {
  //     //         return;
  //     //       }
  //     //       report_data.dishes = item.dishes;
  //     //       report_data.advanced = item.advanced;
  //     //       if (result.buttonIndex === 0) {
  //     //         report_data.count = 1;
  //     //         if (report_data.advanced) {
  //     //           report_data.price = item.price;
  //     //           report_data.repast_title =
  //     //             windowData.value[curMealtimeIndex.value].repast_title;
  //     //           app.report_data = report_data;
  //     //           app.select_order_list = report_data.dishes;
  //     //           router.push({
  //     //             name: "reportSenior",
  //     //             // url: "/reportSenior",
  //     //             // params: report_data,
  //     //           });
  //     //         } else {
  //     //           post_reapst(postUrl, report_data);
  //     //         }
  //     //       }
  //     //       if (result.buttonIndex === 1) {
  //     //         report_data.price = item.price;
  //     //         isShowInput.value = true;
  //     //       }
  //     //     },
  //     //     onFail(err) {},
  //     //   });
  //     //   return;
  //     // }
  //   } else {
  //     postUrl = "apply/post_apply_cancel";
  //     report_data = {
  //       bill_id: item.bill_id,
  //     };
  //     if (item.cancel_approval) {
  //       proxy.$_dd.device.notification.actionSheet({
  //         title: "取消报餐", //标题
  //         cancelButton: "取消", //取消按钮文本
  //         otherButtons: ["因公退费", "因私退费"],
  //         onSuccess: (result) => {
  //           if (result.buttonIndex === -1) {
  //             return;
  //           }
  //           report_data.cancel_type = result.buttonIndex;
  //           post_reapst(postUrl, report_data);
  //         },
  //         onFail(err) { },
  //       });
  //       return;
  //     }
  //     post_reapst(postUrl, report_data);
  //   }
  // }
};
const post_reapst = (url, data) => {
  curItem.value.loading = true;
  proxy
      .$post(url, data)
      .then((res) => {
        if (res && !res.errcode) {
          res = res.result;
          showToast(
              `${url.includes("post_apply_cancel") ? "取消报餐" : "报餐"}成功`
          );
        } else {
          throw res ? res.errmsg : '请求失败，响应为空';
        }
      })
      .catch((err) => {
        console.log(err);
        showToast(err);
      })
      .finally(() => {
        reportNumber.value = null;
        getWindow();
      });
};
const onDateSelect = (day) => {
  let dateStr = dayjs(day).format("YYYY-MM-DD");
  nowDate.value = dateStr;
  curDateIndex.value = dateLs.findIndex(
      (el) => el.dayjs.format("YYYY-MM-DD") === dateStr
  );
  getWindow();
};
const onRepastClick = (i) => {
  curMealtimeIndex.value = i;
};
var handleDate = () => {
  let dateLs = [];
  let week = ["日", "一", "二", "三", "四", "五", "六"];
  let dateMap = ["今天", "明天"];
  nowDate.value = dayjs().format("YYYY-MM-DD");
  for (let i = 0; i < 6; i++) {
    let date = dayjs().add(i, "day");
    dateLs.push({
      week: "周" + week[date.day()],
      date: dateMap[i] || date.format("MM-DD"),
      dayjs: date,
    });
  }
  return dateLs;
};
let dateLs = handleDate();
// emit 控制显示
const emit = defineEmits(["isStatus"]);
const dingdingChange = (e) => {
  // 更新本地储存数据
  localStorage.setItem("dininghall", e);
  getWindow();
};
const getDininghall = () => {
  proxy
      .$get(config.curl.ls, config.postData)
      .then((res) => {
        if (res && res.code === 200) {
          res = res.data;
          res.forEach((element) => {
            element.text = element.title;
            element.value = element.id;
          });
          option = res;
          curDininghall.value = parseInt(localStorage.getItem("dininghall")) ? parseInt(localStorage.getItem("dininghall")) : res[0].id;
          getWindow();
        } else {
          throw res ? res.msg : '请求失败，响应为空';
        }
      })
      .catch((err) => {
        console.log(err);
        showToast(err);
      })
      .finally(() => {
      });
};
let controller = null;

const getWindow = () => {
  windowData.value = [];
  if (controller) {
    controller.abort();
  }
  controller = new AbortController();
  proxy
      .$get(config.curl.window,
          {
            date: nowDate.value,
            dininghall_id: localStorage.getItem('dininghall'),
          },
      )
      .then((res) => {
        if (res && res.code === 200) {
          res = res.data;
          res.forEach((item) => {
            item.stalls.forEach((el) => {
              el.repast_start_time = item.start_time;
              el.repast_end_time = item.end_time;
              el.repast_title = item.title;
              if (el.dishes && typeof el.dishes == "string") {
                el.dishes = JSON.parse(el.dishes);
              }
            });
          });
          // console.log("请求数据------》", res);
          handleMealTime(nowDate.value, res);
          windowData.value = res;
        } else {
          throw res ? res.msg : '请求失败，响应为空';
        }
      })
      .catch((err) => {
        // console.log(err);
        // if (err.code != "ERR_CANCELED") {
        //   showToast(err);
        // }
      })
      .finally(() => {
        curItem.value && (curItem.value.loading = false);
      });
};
const handleMealTime = (nowStrDate, res) => {
  let index = [];
  let timeoutIndex = [];
  let current_time = dayjs().format("HHmmss") * 1;
  let pre_start = null;
  let pre_end = null;
  res.findIndex((el, i) => {
    // 使用原始的 start_time 和 end_time 字段
    let start = el.start_time.split(":").join("") * 1;
    let end = el.end_time.split(":").join("") * 1;
    if (end < start) {
      timeoutIndex.push(i);
      end += 240000;
    }
    // console.log("计算餐时------------->", start, current_time, end);
    if (start < current_time && current_time < end) {
      index.push(i);
    } else if (pre_end < current_time && current_time < start) {
      index.push(i);
    } else if (!pre_end && current_time < start) {
      index.push(i);
    } else {
      pre_start = start;
      pre_end = end;
    }
  });
  let curMealIndex = index[0];
  if (!windowData.value.length) {
    !curMealIndex
        ? (curMealtimeIndex.value = 0)
        : (curMealtimeIndex.value = curMealIndex);
  }
  res.forEach((el, i) => {
    if (index.includes(i)) {
      el.timeOutText = `剩余${Math.abs(
          (timeoutIndex.includes(i)
                  ? dayjs(`${nowStrDate} ${el.end_time}`).add(1, "day")
                  : dayjs(`${nowStrDate} ${el.end_time}`)
          ).diff(dayjs(), "minute") / 60
      ).toFixed(1)}小时`;
    } else {
      el.timeOutText = "暂停就餐";
      el.notShowReport = true;
    }
    if (dayjs().isBefore(dayjs(nowStrDate))) {
      el.timeOutText = "暂停就餐";
      el.notShowReport = true;
    }
    // 格式化时间显示，使用原始的 start_time 和 end_time
    el.repast_start_time = dayjs(
        `${nowStrDate} ${el.start_time}`
    ).format("HH:mm");
    el.repast_end_time = dayjs(`${nowStrDate} ${el.end_time}`).format(
        "HH:mm"
    );
    el.stalls.forEach((window) => {
      window.repast_start_time = el.repast_start_time;
      window.repast_end_time = el.repast_end_time;
    });
  });
};
getDininghall();
</script>
<style lang="scss">
.wrapper-home {
  width: 100%;
  min-height: 100vh;

  overflow: hidden;

  .top-block {
    height: 140px;
    background: #007fff;
    color: #fff;
    overflow: hidden;
    position: relative;
    padding: 0;
    margin-bottom: 8px;

    .dininghall-date {
      display: flex;
      align-items: center;
      background: #007fff;
      padding-top: 12px;
      // padding-left: 8px;

      .van-dropdown-menu {
        flex: 1;

        .van-dropdown-menu__bar {
          background: #007fff;
          box-shadow: none !important;

          .van-dropdown-menu__title {
            color: #fff;
          }

          .van-dropdown-menu__title:after {
            border-color: transparent transparent #fff #fff;
          }

          .van-dropdown-menu__item {
            justify-content: flex-start;
          }
        }

        .van-popup--top {
          left: 16px;
          width: auto;
          right: 16px;
        }
      }

      .van-icon {
        width: 50px;
        text-align: center;
      }
    }

    .date-block {
      font-size: 12px;
      text-align: center;
      width: calc((100% - 5px * 14) / 7);
      height: 56px;
      border-radius: 10px;
      margin: 5px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .date {
      position: relative;
    }

    .date:after {
      position: absolute;
      left: 45%;
      bottom: 0px;
      margin-top: -5px;
      border: 3px solid;
      border-color: transparent transparent #fff #fff;
      transform: rotate(-45deg);
      opacity: 0.8;
      content: "";
    }

    .date:before {
      position: absolute;
      left: -2px;
      top: 50%;
      transform: translateY(-50%);
      height: 45%;
      border-left: 1px solid #fff;
      content: "";
    }

    .cur-date {
      background: #fff !important;
      color: #007fff !important;
    }

    .order,
    .report {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 10px;
      margin-right: 12px;
      background: #007fff;
    }
  }

  .window-info {
    position: relative;
    border-radius: 8px;
    margin-top: 8px;

    .left {
      position: absolute;
      left: 0;
      top: 0;
      width: 80px;
      // height: 60px;
      // width: 74px;
      overflow: scroll;
      height: calc(100vh - 100px);
      padding-bottom: 20px;

      .repast-time {
        width: 80px;
        height: 60px;
        display: flex;

        .select-line {
          width: 3px;
          background: #007fff;
        }

        .text-info {
          width: 80px;
          height: 60px;
          flex: 1;
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          font-size: 14px;

          .repast-text {
            font-size: 14px;
            margin: 20px 12px 21px 12px;
            // margin-top: px;
            text-align: center;
          }
        }
      }

      .active-repast {
        width: 80px;
        height: 60px;
        background: #fff;
        border-radius: 0 8px 8px 0;
      }
    }

    .right {
      margin-left: 74px;
      height: calc(100vh - 222px);
      overflow: scroll;
      padding-bottom: 20px;

      .window-dish {
        margin-left: 8px;
        background: #fff;
        border-radius: 8px;
        margin-bottom: 8px;
        margin-right: 8px;
      }

      .title-block {
        font-size: 12px;
        padding: 18px 8px 12px;
        // background: rgba(245, 247, 250, 0.88);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .dish-block {
        font-size: 10px;
        color: rgba(23, 26, 29, 0.4);
        margin: 0 8px;
        padding: 12px 0;
        border-top: 1px solid #f6f6f6;
        border-bottom: 1px solid #f6f6f6;
        overflow: scroll;
        white-space: nowrap;

        .dish-item {
          text-align: center;
          width: 50px;
          margin-right: 8px;
          display: inline-block;

          .title {
            margin-top: 2px;
            width: 100%;
            overflow: auto;
          }
        }
      }

      .bottom-block {
        color: rgba(23, 26, 29, 0.24);
        font-size: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 8px;
        padding: 12px 0;
      }
    }
  }

  .dish-detail {
    width: 80vw;
    border-radius: 4px;
    overflow: hidden;

    .title {
      padding: 16px 16px 0;
      font-size: 17px;
    }

    .dish-title {
      max-width: 100%;
      overflow: hidden;
      word-break: break-all;
    }
  }

  .van-divider {
    margin: 14px 0 0;
  }
}
</style>
