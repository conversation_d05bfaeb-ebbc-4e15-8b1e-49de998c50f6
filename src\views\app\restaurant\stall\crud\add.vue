<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="add"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'
import { getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance();
const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    // 如果有id参数，则为编辑模式
    ...(route.query.id ? { id: route.query.id } : {}),
    dininghall_id:localStorage.getItem('dininghall')
  },
  curl: {
    add: '/stall/post_add', // 新增接口
    edit: '/stall/post_modify', // 编辑接口
    info: '/stall/get_info' // 获取详情接口（编辑时需要）
  },
  groupForm: [
    [0, 1],
    [1, 3]
  ],
  form: [
    {
      label: "档口名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请填写用户名" }],
    },
    {
      label: "消费机",
      key: "device_consumptions",
      component: "yhc-picker",
      placeholder: "请选择",
      ellipsis:32,
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false
      },
      card: {
        title: "title",
        desc: "desc"
      },
      opts: {
        url: "/device/get_device_all",
        postData: {type:1},
        merge: false,
        multiple: true,
        text_key: "title",
        contrast_key: "id",
        keyMap: [{id: "id", title: "title"}],
        defaultList: []
      },
    },
    {
      label: "收银机",
      key: "device_cashiers",
      component: "yhc-picker",
      placeholder: "请选择",
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false
      },
      card: {
        title: "title",
      },
      opts: {
        url: "/device/get_device_all",
        postData: {type: 0},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: {id: "id", title: "title"},
        defaultList: []
      },
    },
  ]
}


const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改档口' : '新增档口',
  });
};
setRightA()
setRight()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
