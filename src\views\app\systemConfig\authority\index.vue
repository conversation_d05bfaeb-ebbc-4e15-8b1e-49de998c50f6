<template>
  <div class="wrapper-dish">
    <yhc-list
      ref="yhclist"
      :config="config"
      @onButClick="onButClick"
    >
     <template #header>
        <div class="add-button-container">
          <div class="add-button" @click="onAddClick">
            <img src="/img/add.svg" alt="新增" class="add-icon" />
            <span>新增权限组</span>
          </div>
        </div>
      </template>
       <template #default="{ item}">
         <div class="sort-item" @click.stop="onCardClick(item)">
           <div class="title">
             <span>{{ item.title }}</span>
           </div>
           <div class="line">
             <div class="name">管理员：{{ item.admin_names }}</div>
             <div class="name">分配权限：{{ item.action_titles }}</div>
           </div>
         </div>

      </template>
    </yhc-list>
  </div>
</template>
<script setup>
import { ref } from "vue";
const router = useRouter();
let yhclist = ref(null);
const skeletonConfig = reactive({
  isShow: false,
  count: 3,
  row: 2,
  rowWidth: ['100%', '60%', '80%'],
  avatar: true,
  avatarSize: '40px',
  avatarShape: 'round',
  title: true,
  titleWidth: '50%',
  duration: 500
})
let config = {
  curl: {
    ls: "permission_group/get_ls",
    del: "permission_group/post_del",
  },
  postData: {},
  search: {
    isShow: true,
    isShowPopup: false
  },
  tabs: {
    isShow: false,
  },
   button: {
    isShow: false,
  },
  skeleton: skeletonConfig,
  format: (data) => {
  },
};
  
const onButClick = (e) => {
  router.push({ path: "/systemConfig/add" });
};
const onCardClick = (item) => {
  router.push({ path: "/systemConfig/authority/info", query: { id: item.id } });
};
</script>
<style lang="scss" scoped>
.wrapper-dish {
  width: 100%;
  min-height: 100vh;
  .add-button-container {
  padding: 16px;
  padding-bottom: 0;
  .add-button {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    .add-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: normal;
      line-height: 22px;
      color: #323233;
    }
  }
}
  .bill-item {
    margin: 16px;
    background: #fff;
    border-radius: 4px;
    padding: 16px;
    color: rgba(23, 26, 29, 0.6);

    .item-top {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      margin-bottom: 8px;
      color: rgba(23, 26, 29, 1);
      font-size: 17px;
    }
    .item-content {
      display: flex;
      justify-content: space-between;
      font-size: 13px;
      color: rgba(23, 26, 29, 0.6);
      border-top: 1px solid #f6f6f6;
      padding-top: 12px;
      .item-content-left {
        display: flex;
        justify-content: space-around;
        flex-direction: column;
      }
    }
  }
}

.sort-item {
  // display: flex;
  // justify-content: space-between;
  margin-left: 5%;
  margin-top: 12px;
  padding: 16px;
  width: 90%;
  // height: 79px;
  border-radius: 6px;
  background-color: #fff;
  box-sizing: border-box;

  .title {
    display: flex;
    justify-content: space-between;
    align-content: center;
    margin-bottom: 16px;
    font-size: 17px;
  }

  .line {
    margin-bottom: 16px;
    font-size: 13px;
    color: #a2a3a5;
    line-height: 21px;

    .img {
      margin-right: 6px;
      width: 21px;
      height: 21px;
      border-radius: 3px;
      background-color: #007fff;
      color: #fff;
      text-align: center;

      img {
        width: 100%;
        height: 100%;
        border-radius: 3px;
      }
    }

    .name {
      position: relative;
      padding-right: 12px;
    }

    .time {
      margin-left: 12px;
    }
  }
  .type {
    display: inline-block;
    padding: 2px 4px;
    border-radius: 6px;
    font-size: 10px;
    line-height: 17px;
    background-color: #e0efff;
    color: #007fff;
  }
}
</style>
