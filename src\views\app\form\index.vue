<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form :config="basicFormConfig" @submit="onBasicSubmit" />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {}, // 添加必需的postData
  curl: {},
  groupForm: [
    [0, 1],
    [1, 3],
    [3, 4],
    [4, 5],
    [5, 6], 
    [6, 7],
    [7, 8],
    [8, 19]  // 更新后续索引
  ],
  form: [
    {
      label: "外部人员",
      key: "title",
      component: "yhc-select-outsiders",
      placeholder: "请选择外部人员",
      required: true,
      rules: [{ required: true, message: "请选择外部人员" }],
    },
    {
      label: "用户名",
      key: "basic-input",
      component: "yhc-input",
      type: "text",
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请填写用户名" }],
    },
    {
      label: "复选框（默认选中A，不可取消）",
      key: "checkbox-test",
      component: "yhc-checkbox-group",
      options: [
        {
          label: "复选框A（默认选中）",
          value: "A",
        },
        {
          label: "复选框B（可选）",
          value: "B",
        },
        {
          label: "复选框C（可选）",
          value: "C",
        },
      ],
      required: true,
      defaultValue: "A", // 默认选中A，且不可取消选中
      direction: "horizontal", // 布局方向：horizontal 或 vertical
      rules: [{ required: true, message: "请选择复选框" }],
    },
    {
      label: "复选框（垂直布局，多个默认值）",
      key: "checkbox-vertical",
      component: "yhc-checkbox-group",
      options: [
        {
          label: "选项1（默认选中）",
          value: "option1",
        },
        {
          label: "选项2（默认选中）",
          value: "option2",
        },
        {
          label: "选项3（可选）",
          value: "option3",
        },
        {
          label: "选项4（禁用）",
          value: "option4",
          disabled: true,
        },
      ],
      required: true,
      defaultValue: ["option1", "option2"], // 多个默认值
      direction: "vertical", // 垂直布局
      rules: [{ required: true, message: "请选择选项" }],
    },
    // yhc-radio-group 测试配置
    {
      label: "适用人员",
      key: "radio-basic",
      component: "yhc-radio-group",
      options: [
        { value: "all", label: "全部人员" },
        { value: "specific", label: "指定人员" }
      ],
      required: true,
      rules: [{ required: true, message: "请选择适用人员" }],
    },
    {
      label: "适用餐时",
      key: "radio-showmode",
      component: "yhc-radio-group",
      options: [
        { value: "all", label: "全部餐时" },
        { value: "specific", label: "指定餐时" }
      ],
      shape:"dot",
      required: true,
      direction: "horizontal",
      defaultValue: "all",
      child: {
        showMode: "specific", // 当选中"指定餐时"时显示子表单
        form: [
          {
            label: "指定餐时",
            key: "specific-meal-times",
            component: "yhc-input",
           
          }
        ]
      }
    },
    {
      label: "用户类型（map配置子表单）",
      key: "radio-map",
      component: "yhc-radio-group",
      options: [
        { value: "all", label: "全部人员" },
        { value: "department", label: "指定部门" },
        { value: "user", label: "指定用户" }
      ],
      required: true,
      direction: "vertical",
      child: {
        map: {
          "department": [
            {
              label: "部门名称",
              key: "department-name",
              component: "yhc-input",
              type: "text",
              placeholder: "请输入部门名称",
              required: true,
            }
          ],
          "user": [
            {
              label: "用户姓名",
              key: "user-name",
              component: "yhc-input",
              type: "text",
              placeholder: "请输入用户姓名",
              required: true,
            },
            {
              label: "用户工号",
              key: "user-id",
              component: "yhc-input",
              type: "text",
              placeholder: "请输入用户工号",
              required: true,
            }
          ]
        }
      }
    },
    {
      label: "分段器",
      key: "segmented-test",
      component: "yhc-segmented-control",
      options: [
        { text: "人员", value: "personal" },
        { text: "信息", value: "work" },
        // { text: "联系方式", value: "contact" }
      ],
      required: true,
      child: {
        map: {
          "personal": [
            {
              label: "姓名",
              key: "personal-name",
              component: "yhc-input",
              type: "text",
              placeholder: "请输入姓名",
              required: true,
            },
            {
              label: "年龄",
              key: "personal-age",
              component: "yhc-input",
              type: "number",
              placeholder: "请输入年龄",
              required: true,
            }
          ],
          "work": [
            {
              label: "公司名称",
              key: "work-company",
              component: "yhc-input",
              type: "text",
              placeholder: "请输入公司名称",
              required: true,
            },
            {
              label: "职位",
              key: "work-position",
              component: "yhc-input",
              type: "text",
              placeholder: "请输入职位",
              required: true,
            }
          ],
          "contact": [
            {
              label: "手机号",
              key: "contact-phone",
              component: "yhc-input",
              type: "tel",
              placeholder: "请输入手机号",
              required: true,
            },
            {
              label: "邮箱",
              key: "contact-email",
              component: "yhc-input",
              type: "email",
              placeholder: "请输入邮箱",
              required: true,
            }
          ]
        }
      }
    },
    {
      label: "分段器",
      key: "segmented-showmode",
      component: "yhc-segmented-control",
      options: [
        { text: "显示更多选项", value: "show" },
        { text: "隐藏更多选项", value: "hide" }
      ],
      required: true,
      child: {
        showMode: "show", // 当选中"显示更多选项"时显示子表单
        form: [
          {
            label: "附加信息",
            key: "additional-info",
            component: "yhc-input",
            type: "text",
            placeholder: "请输入附加信息",
            required: true,
          },
          {
            label: "备注",
            key: "remarks",
            component: "yhc-input",
            type: "textarea",
            placeholder: "请输入备注",
            autosize: true,
            "show-word-limit": true,
            maxlength: 200,
          }
        ]
      }
    },
    {
      label: "开始时间",
      key: "speed_duration",
      component: "yhc-picker-date",
      type: "time",
      required: true,
      typeLabel: "start",
      crossDayConfig: {
        startTimeKey: "speed_duration",
        endTimeKey: "speed_time"
      },
    },
    {
      label: "结束时间",
      key: "speed_time",
      component: "yhc-picker-date",
      type: "time",
      required: true,
      typeLabel: "end",
      crossDayConfig: {
        startTimeKey: "speed_duration",
        endTimeKey: "speed_time"
      },
    },
    {
      label: "开始时间2",
      key: "speed_duration2",
      component: "yhc-picker-date",
      type: "time",
      required: true,
    },
    // yhc-stepper
    {
      label: "步进器",
      key: "speed",
      component: "yhc-stepper",
      default: 2, // 默认值
      min: 1, // 最小值
      max: 10, // 最大值
      step: 1, // 步长
      theme: "round" // 样式 round:圆角 ，default：方形
    },
    {
      label: "超速提醒",
      key: "speeding_alert",
      component: "yhc-switch",
      child: {
        showMode: true,
        form: [
          {
            label: "超速提醒1",
            key: "speeding_alert1",
            component: "yhc-switch",
            child: {
              showMode: true,
              form: [
                {
                  label: "超过速度提醒2",
                  key: "speed_threshold",
                  component: "yhc-switch",
                  child: {
                    showMode: true,
                    form: [
                      {
                        label: "超过速度提醒3",
                        key: "speed_threshold3",
                        component: "yhc-switch",
                        child: {
                          showMode: true,
                          form: [
                            {
                              label: "超过速度提醒4",
                              key: "speed_threshold4",
                              component: "yhc-switch",
                              child: {
                                showMode: true,
                                form: [
                                  {
                                    label: "超过速度提醒4",
                                    key: "spe",
                                    component: "yhc-input",
                                    // child: {
                                    //   showMode: true,
                                    //   form: [
                                    //     {
                                    //       label: "速度阈值",
                                    //       name: "speed_threshold4",
                                    //       component: "yhc-input",
                                    //     }
                                    //   ]
                                    // }
                                  }
                                ],
                                
                              },
                            },
                          ],
                        },
                      },
                      {
                        label: "超过速度提醒2",
                        key: "speed_threshold2",
                        component: "yhc-switch",
                        child: {},
                      }
                    ],
                  },
                },
              ],

            },
          },
        ],
        formChild: [
          {
            label: "1231",
            key: "speed_",
            component: "yhc-input",
            type: "number",
          },
        ],
      },
    },
    {
      label: "个人简介",
      key: "basic-textarea",
      component: "yhc-input",
      type: "textarea",
      placeholder: "请输入",
      autosize: true,
      "show-word-limit": true,
      maxlength: 200,
      desc: "支持多行文本输入，带字数限制"
    },
    {
      label: "年龄",
      key: "basic-number",
      component: "yhc-input",
      type: "number",
      placeholder: "请输入",
      "right-icon": "岁",
    },
    {
      label: "价格（小数位格式化）",
      key: "price-decimal",
      component: "yhc-input",
      type: "number",
      placeholder: "请输入价格",
      "right-icon": "元",
      decimalPlaces: true,
      desc: "启用decimalPlaces后，只允许输入最多两位小数，保持用户输入的原始格式显示"
    },
    {
      label: "部门选择多选",
      key: "select-picker",
      component: "yhc-picker",
      placeholder: "请选择",
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false
      },
      card: {
        title: "title",
        desc: "id"
      },
      opts: {
        url: "",
        postData: {},
        merge: false,
        multiple: true,
        text_key: "title",
        contrast_key: "id",
        keyMap: {},
        defaultList: [
          { id: 1, title: "技术部" },
          { id: 2, title: "市场部" },
          { id: 3, title: "人事部" },
          { id: 4, title: "财务部" },
          { id: 5, title: "技术部" }
        ]
      },
    },
    {
      label: "部门选择单选",
      key: "select",
      component: "yhc-picker",
      placeholder: "请选择",
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false
      },
      card: {
        title: "title",
      },
      opts: {
        url: "",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: {},
        defaultList: [
          { id: 1, title: "技术部" },
          { id: 2, title: "市场部" },
          { id: 3, title: "人事部" },
          { id: 4, title: "财务部" }
        ]
      },
    },
  ]
}


// 表单提交处理函数
const onBasicSubmit = (data) => {
  console.log('基础表单提交:', data)
  console.log('复选框测试数据:', {
    'checkbox-test': data['checkbox-test'],
    'checkbox-vertical': data['checkbox-vertical']
  })
  console.log('单选框组测试数据:', {
    'radio-basic': data['radio-basic'],
    'radio-showmode': data['radio-showmode'],
    'radio-map': data['radio-map']
  })
  console.log('单选框组子表单数据:', {
    'specific-meal-times': data['specific-meal-times'],
    'department-name': data['department-name'],
    'user-name': data['user-name'],
    'user-id': data['user-id']
  })
  console.log('分段器测试数据:', {
    'segmented-test': data['segmented-test'],
    'segmented-showmode': data['segmented-showmode']
  })
  console.log('子表单数据:', {
    'personal-name': data['personal-name'],
    'personal-age': data['personal-age'],
    'work-company': data['work-company'],
    'work-position': data['work-position'],
    'contact-phone': data['contact-phone'],
    'contact-email': data['contact-email'],
    'additional-info': data['additional-info'],
    'remarks': data['remarks']
  })
  showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
