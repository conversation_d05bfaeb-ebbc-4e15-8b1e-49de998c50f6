<template>
  <div class="wrapper-select-outsiders" :class="{ 'required-field': config.required }">
    <van-field
      v-model="data.value"
      is-link
      :name="config.key"
      :label="config.label"
      :type="config.type"
      :placeholder="config.placeholder"
      :required="config.required"
      :rules="config.rules"
      :disabled="config.disabled"
      readonly
      input-align="left"
      @click="onClick"
    />

    <!-- 外部人员选择弹窗 -->
    <van-popup
      v-model:show="showSelector"
      position="right"
      :style="{ width: '100%', height: '100%' }"
      :close-on-click-overlay="false"
      @update:show="onPopupClose"
    >
      <div class="outsiders-selector">
        <!-- 搜索框 -->
        <div class="search-container">
          <van-search
            v-model="searchKeyword"
            :placeholder="`搜索：${currentDepartment.name || '外部组织'}`"
            @input="onSearch"
          />
          
        </div>

        <!-- 通讯录标题和面包屑 -->
        <div class="address-book-header">
          <div class="header-left">
            <span class="title">通讯录 <van-icon name="arrow" size="12" /></span>
            <div class="breadcrumb-nav" v-if="breadcrumbs.length > 1">
              <span
                v-for="(item, index) in breadcrumbs"
                :key="item.id"
                class="breadcrumb-item"
                @click="navigateTo(item, index)"
              >
                {{ item.name }}
                <van-icon name="arrow" v-if="index < breadcrumbs.length - 1" class="breadcrumb-arrow" />
              </span>
            </div>
            <span v-else class="subtitle">{{ currentDepartment.name || '外部组织' }}</span>
          </div>
        </div>

        <!-- 全选选项 -->
        <div class="select-all-container" v-if="currentMembers.length > 0 || filteredDepartments.length > 0">
          <div class="custom-cell select-all-cell" @click="toggleSelectAll">
            <div class="cell-left">
              <div class="select-icon" :class="{ 'selected': isAllSelected }">
                <van-icon name="success" v-if="isAllSelected" />
              </div>
              <span class="cell-title">全选</span>
            </div>
          </div>
        </div>

        <!-- 列表内容 -->
        <div class="content-container">
          <!-- 部门列表 -->
          <div class="department-list" v-if="filteredDepartments.length > 0">
            <div
              v-for="(dept, index) in filteredDepartments"
              :key="'dept-' + dept.id"
              class="custom-cell department-cell"
              :class="{
                'selected': isDepartmentSelected(dept),
                'last-department': index === filteredDepartments.length - 1
              }"
            >
              <div class="cell-left" @click="selectDepartment(dept)">
                <div class="select-icon" :class="{ 'selected': isDepartmentSelected(dept) }">
                  <van-icon name="success" v-if="isDepartmentSelected(dept)" />
                </div>
                <span class="cell-title">{{ dept.name }}({{ dept.memberCount }}人)</span>
              </div>
              <div class="cell-right">
                <div class="divider"></div>
                <div
                  class="next-level"
                  :class="{ 'disabled': isDepartmentSelected(dept) }"
                  @click="!isDepartmentSelected(dept) && enterDepartment(dept)"
                >
                  <van-icon name="arrow" />
                  <span>下级</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 当前部门的直属成员列表 -->
          <div class="member-list" v-if="filteredMembers.length > 0">
            <div
              v-for="member in filteredMembers"
              :key="'member-' + member.id"
              class="custom-cell member-cell"
              :class="{ 'selected': isSelected(member) }"
              @click="selectMember(member)"
            >
              <div class="cell-left">
                <div class="select-icon" :class="{ 'selected': isSelected(member) }">
                  <van-icon name="success" v-if="isSelected(member)" />
                </div>
                <div class="member-avatar" v-if="member.avatar">
                  <img :src="member.avatar" :alt="member.name" />
                </div>
                <div class="member-avatar default-avatar" v-else>
                  {{ getNameInitials(member.name) }}
                </div>
                <div class="member-info">
                  <span class="cell-title">{{ member.name }}</span>
                  <!-- <span class="member-position">{{ member.position }}</span> -->
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <van-empty
            v-if="filteredDepartments.length === 0 && filteredMembers.length === 0 && !searchKeyword"
            description="暂无数据"
          />
        </div>

        <!-- 底部已选择信息 -->
        <div class="bottom-selected" v-if="tempSelectedList.length > 0">
          <div class="selected-info">
            <span>{{ getTempSelectedSummary() }}：</span>
            <div class="selected-avatars" @click="showSelectedDetail = true">
              <div
                v-for="item in tempSelectedList.slice(0, 5)"
                :key="item.id + '-' + item.type"
                class="avatar-item"
                :class="{ 'department-avatar': item.type === 'department' }"
              >
                <van-icon :name="item.type === 'department' ? 'cluster-o' : 'contact'" />
              </div>
              <span v-if="tempSelectedList.length > 5" class="more-count">+{{ tempSelectedList.length - 5 }}</span>
            </div>
          </div>
          <van-button type="primary" size="small" @click="onConfirm">
            确定
          </van-button>
        </div>


      </div>
    </van-popup>

    <!-- 已选择人员详情弹窗 -->
    <van-popup
      v-model:show="showSelectedDetail"
      position="bottom"
      :style="{ height: '50%' }"
    >
      <div class="selected-detail-popup">
        <div class="popup-header">
          <span></span>
          <span>已选择({{ tempSelectedList.length }})</span>
          <span class="confirm-btn" @click="showSelectedDetail = false">确定</span>
        </div>

        <div class="selected-list">
          <div
            v-for="item in tempSelectedList"
            :key="item.id + '-' + item.type"
            class="selected-item"
          >
            <div class="item-info">
              <div class="avatar" :class="{ 'department-avatar': item.type === 'department' }" v-if="item.type === 'department'">
                <van-icon name="cluster-o" />
              </div>
              <div class="avatar member-avatar-detail" v-else>
                <img v-if="item.avatar" :src="item.avatar" :alt="item.name" />
                <span v-else class="avatar-text">{{ getNameInitials(item.name) }}</span>
              </div>
              <div class="info-text">
                <div class="name">{{ item.name }}</div>
                <div class="position" v-if="item.type === 'member'">{{ item.position }}</div>
                <div class="position" v-else>({{ item.memberCount }}人)</div>
                <div class="department" v-if="item.departmentName">{{ item.departmentName }}</div>
              </div>
            </div>
            <van-button
              size="mini"
              plain
              @click="removeSelected(item)"
            >
              移除
            </van-button>
          </div>
        </div>

        <div class="popup-footer" @click="showSelectedDetail = false">
          取消
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { showToast } from 'vant'
import { deepAssign } from "@/untils"
import { useRouter } from 'vue-router'

// 路由实例
const router = useRouter()

// 默认配置
let config = {
  label: "外部人员",
  key: "outsiders",
  type: "text",
  placeholder: "请选择外部人员",
  required: false,
  disabled: false,
  rules: [],
}

// Props定义
const props = defineProps({
  config: Object,
  form: Object,
})

// 合并配置
props.config && deepAssign(config, props.config)

// 组件数据
const data = reactive({
  value: "",
  selectedList: [], // 已选择的人员列表
})

// 组件状态（简化后只需要基本状态）

// 静态数据 - 模拟外部组织架构
const mockData = {
  departments: [
    { id: 1, name: '合作伙伴', parentId: 0, memberCount: 15 },
    { id: 2, name: '供应商', parentId: 0, memberCount: 8 },
    { id: 3, name: '客户单位', parentId: 0, memberCount: 12 },
    { id: 11, name: '技术合作方', parentId: 1, memberCount: 6 },
    { id: 12, name: '业务合作方', parentId: 1, memberCount: 9 },
    { id: 21, name: '原材料供应商', parentId: 2, memberCount: 4 },
    { id: 22, name: '设备供应商', parentId: 2, memberCount: 4 },
    { id: 31, name: '企业客户', parentId: 3, memberCount: 7 },
    { id: 32, name: '政府客户', parentId: 3, memberCount: 5 },
  ],
  members: [
    // 合作伙伴 - 技术合作方
    { id: 101, name: '张技术', position: '技术总监', departmentId: 11 },
    { id: 102, name: '李开发', position: '高级工程师', departmentId: 11 },
    { id: 103, name: '王架构', position: '架构师', departmentId: 11 },
    { id: 104, name: '赵测试', position: '测试经理', departmentId: 11 },
    { id: 105, name: '刘运维', position: '运维工程师', departmentId: 11 },
    { id: 106, name: '陈产品', position: '产品经理', departmentId: 11 },

    // 合作伙伴 - 业务合作方
    { id: 201, name: '孙业务', position: '业务总监', departmentId: 12 },
    { id: 202, name: '周销售', position: '销售经理', departmentId: 12 },
    { id: 203, name: '吴市场', position: '市场专员', departmentId: 12 },
    { id: 204, name: '郑客服', position: '客服主管', departmentId: 12 },
    { id: 205, name: '王商务', position: '商务经理', departmentId: 12 },
    { id: 206, name: '李渠道', position: '渠道经理', departmentId: 12 },
    { id: 207, name: '张推广', position: '推广专员', departmentId: 12 },
    { id: 208, name: '刘策划', position: '策划经理', departmentId: 12 },
    { id: 209, name: '陈运营', position: '运营专员', departmentId: 12 },

    // 供应商 - 原材料供应商
    { id: 301, name: '钱采购', position: '采购经理', departmentId: 21 },
    { id: 302, name: '孙质检', position: '质检员', departmentId: 21 },
    { id: 303, name: '李仓管', position: '仓库管理员', departmentId: 21 },
    { id: 304, name: '周物流', position: '物流专员', departmentId: 21 },

    // 供应商 - 设备供应商
    { id: 401, name: '吴设备', position: '设备工程师', departmentId: 22 },
    { id: 402, name: '郑维修', position: '维修技师', departmentId: 22 },
    { id: 403, name: '王安装', position: '安装工程师', departmentId: 22 },
    { id: 404, name: '李调试', position: '调试工程师', departmentId: 22 },

    // 客户单位 - 企业客户
    { id: 501, name: '张企业', position: '采购总监', departmentId: 31 },
    { id: 502, name: '李决策', position: '决策者', departmentId: 31 },
    { id: 503, name: '王联络', position: '联络人', departmentId: 31 },
    { id: 504, name: '赵财务', position: '财务经理', departmentId: 31 },
    { id: 505, name: '刘法务', position: '法务专员', departmentId: 31 },
    { id: 506, name: '陈项目', position: '项目经理', departmentId: 31 },
    { id: 507, name: '孙验收', position: '验收专员', departmentId: 31 },

    // 客户单位 - 政府客户
    { id: 601, name: '周政府', position: '处长', departmentId: 32 },
    { id: 602, name: '吴公务', position: '科长', departmentId: 32 },
    { id: 603, name: '郑监管', position: '监管员', departmentId: 32 },
    { id: 604, name: '王审批', position: '审批员', departmentId: 32 },
    { id: 605, name: '李协调', position: '协调员', departmentId: 32 },

    // 外部组织的直属成员（不属于任何子部门）
    { id: 701, name: '陈独立', position: '外部顾问', departmentId: 0 },
    { id: 702, name: '刘自由', position: '技术专家', departmentId: 0 },
    { id: 703, name: '杨单独', position: '业务顾问', departmentId: 0 },
    { id: 704, name: '黄个人', position: '咨询师', departmentId: 0 },
  ]
}

// 计算属性 - 当前部门的子部门
const filteredDepartments = computed(() => {
  const currentDepts = mockData.departments.filter(dept => dept.parentId === currentDepartment.value.id)

  if (!searchKeyword.value) {
    return currentDepts
  }

  return currentDepts.filter(dept =>
    dept.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 计算属性 - 当前部门的成员
const filteredMembers = computed(() => {
  const currentMembers = mockData.members.filter(member => member.departmentId === currentDepartment.value.id)

  if (!searchKeyword.value) {
    return currentMembers
  }

  return currentMembers.filter(member =>
    member.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    member.position.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 计算属性 - 当前部门的所有成员（用于全选）
const currentMembers = computed(() => {
  return mockData.members.filter(member => member.departmentId === currentDepartment.value.id)
})

// 计算属性 - 是否全选（包括当前层级的部门和成员）
const isAllSelected = computed(() => {
  const currentDepts = filteredDepartments.value
  const currentMems = currentMembers.value

  if (currentDepts.length === 0 && currentMems.length === 0) return false

  const allDeptsSelected = currentDepts.every(dept => isDepartmentSelected(dept))
  const allMembersSelected = currentMems.every(member => isSelected(member))

  return allDeptsSelected && allMembersSelected
})

// 点击输入框跳转到选择页面
const onClick = () => {
  if (config.disabled) return

  // 将当前选择数据存储到路由参数或全局状态中
  const currentData = {
    selectedList: data.selectedList,
    formKey: config.key
  }

  // 跳转到外部人员选择页面
  router.push({
    path: '/select-outsiders',
    query: {
      data: JSON.stringify(currentData),
      returnPath: router.currentRoute.value.fullPath
    }
  })
}

// 进入子部门
const enterDepartment = (dept) => {
  currentDepartment.value = dept
  breadcrumbs.value.push(dept)
  searchKeyword.value = ''
}

// 面包屑导航
const navigateTo = (item, index) => {
  currentDepartment.value = item
  breadcrumbs.value = breadcrumbs.value.slice(0, index + 1)
  searchKeyword.value = ''
}

// 全选/取消全选
const toggleSelectAll = () => {
  const currentDepts = filteredDepartments.value
  const currentMems = currentMembers.value

  if (isAllSelected.value) {
    // 取消全选：从临时列表移除当前层级的所有部门和成员
    currentDepts.forEach(dept => {
      const index = tempSelectedList.value.findIndex(item => item.id === dept.id && item.type === 'department')
      if (index > -1) {
        tempSelectedList.value.splice(index, 1)
      }
    })

    currentMems.forEach(member => {
      const index = tempSelectedList.value.findIndex(item => item.id === member.id && item.type === 'member')
      if (index > -1) {
        tempSelectedList.value.splice(index, 1)
      }
    })
  } else {
    // 全选：添加当前层级的所有部门和成员到临时列表
    currentDepts.forEach(dept => {
      if (!isDepartmentSelected(dept)) {
        const deptWithInfo = {
          ...dept,
          type: 'department',
          departmentName: findDepartmentPath(dept.parentId)
        }
        tempSelectedList.value.push(deptWithInfo)
      }
    })

    currentMems.forEach(member => {
      if (!isSelected(member)) {
        const memberWithDept = {
          ...member,
          type: 'member',
          departmentName: findDepartmentPath(member.departmentId)
        }
        tempSelectedList.value.push(memberWithDept)
      }
    })
  }
  // 不立即更新表单数据，只在确认时更新
}

// 选择部门（只选择部门本身，不展开成员）
const selectDepartment = (dept) => {
  const isDeptSelected = isDepartmentSelected(dept)

  if (isDeptSelected) {
    // 取消选择部门：从临时选择列表中移除该部门
    const index = tempSelectedList.value.findIndex(item => item.id === dept.id && item.type === 'department')
    if (index > -1) {
      tempSelectedList.value.splice(index, 1)
    }
  } else {
    // 选择部门：添加部门到临时选择列表
    const deptWithInfo = {
      ...dept,
      type: 'department',
      departmentName: findDepartmentPath(dept.parentId)
    }
    tempSelectedList.value.push(deptWithInfo)
  }
  // 不立即更新表单数据，只在确认时更新
}

// 判断部门是否被选择（使用临时列表）
const isDepartmentSelected = (dept) => {
  return tempSelectedList.value.some(item => item.id === dept.id && item.type === 'department')
}

// 选择成员
const selectMember = (member) => {
  const existingIndex = tempSelectedList.value.findIndex(item => item.id === member.id && item.type === 'member')

  if (existingIndex > -1) {
    // 如果已选择，则取消选择
    tempSelectedList.value.splice(existingIndex, 1)
  } else {
    // 添加到临时选择列表，包含部门信息和类型标识
    const memberWithDept = {
      ...member,
      type: 'member',
      departmentName: findDepartmentPath(member.departmentId)
    }
    tempSelectedList.value.push(memberWithDept)
  }

  // 不立即更新表单数据，只在确认时更新
}

// 判断成员是否已选择（使用临时列表）
const isSelected = (member) => {
  return tempSelectedList.value.some(item => item.id === member.id && item.type === 'member')
}

// 查找部门路径
const findDepartmentPath = (deptId) => {
  const dept = mockData.departments.find(d => d.id === deptId)
  if (!dept) return ''

  if (dept.parentId === 0) {
    return dept.name
  }

  const parentPath = findDepartmentPath(dept.parentId)
  return parentPath ? `${parentPath}/${dept.name}` : dept.name
}

// 更新显示值
const updateDisplayValue = () => {
  if (data.selectedList.length === 0) {
    data.value = ''
    return
  }

  // 统计部门数量
  const departments = data.selectedList.filter(item => item.type === 'department')
  const members = data.selectedList.filter(item => item.type === 'member')

  const deptCount = departments.length
  const memberCount = members.length

  if (deptCount > 0 && memberCount > 0) {
    data.value = `选择${deptCount}部门${memberCount}员工`
  } else if (deptCount > 0) {
    data.value = `选择${deptCount}部门`
  } else if (memberCount > 0) {
    data.value = `选择${memberCount}员工`
  }
}

// 更新表单数据
const updateFormData = () => {
  props.form[config.key] = [...data.selectedList]
}

// 获取面包屑文本
const getBreadcrumbText = () => {
  if (breadcrumbs.value.length <= 1) {
    return '外部组织'
  }

  // 从第二个开始，因为第一个是根节点
  const pathParts = breadcrumbs.value.slice(1).map(item => item.name)
  return pathParts.join(' > ')
}

// 获取选择摘要
const getSelectedSummary = () => {
  const departments = data.selectedList.filter(item => item.type === 'department')
  const members = data.selectedList.filter(item => item.type === 'member')

  const deptCount = departments.length
  const memberCount = members.length

  if (deptCount > 0 && memberCount > 0) {
    return `已选择:${data.selectedList.length}人，其中有${deptCount}个部门`
  } else if (deptCount > 0) {
    return `已选择:${deptCount}个部门`
  } else if (memberCount > 0) {
    return `已选择:${memberCount}人`
  }
  return '已选择:0人'
}

// 获取临时选择摘要
const getTempSelectedSummary = () => {
  const departments = tempSelectedList.value.filter(item => item.type === 'department')
  const members = tempSelectedList.value.filter(item => item.type === 'member')

  const deptCount = departments.length
  const memberCount = members.length

  if (deptCount > 0 && memberCount > 0) {
    return `已选择:${tempSelectedList.value.length}人，其中有${deptCount}个部门`
  } else if (deptCount > 0) {
    return `已选择:${deptCount}个部门`
  } else if (memberCount > 0) {
    return `已选择:${memberCount}人`
  }
  return '已选择:0人'
}

// 搜索处理
const onSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

// 弹窗关闭处理
const onPopupClose = (show) => {
  if (!show) {
    // 弹窗关闭时，重置临时选择为当前已确认的选择
    tempSelectedList.value = [...data.selectedList]
  }
}

// 处理浏览器返回事件
const handlePopState = (event) => {
  if (showSelector.value) {
    // 如果弹窗是打开的，关闭弹窗而不是返回上一页
    event.preventDefault()
    showSelector.value = false
    // 重新添加历史记录，保持在当前页面
    window.history.pushState(null, '', window.location.href)
    return false
  }
}

// 组件挂载时添加返回事件监听
onMounted(() => {
  // 监听浏览器返回事件
  window.addEventListener('popstate', handlePopState)
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('popstate', handlePopState)
})

// 获取姓名后两个字作为头像显示
const getNameInitials = (name) => {
  if (!name) return ''
  return name.length >= 2 ? name.slice(-2) : name
}

// 确认选择并关闭弹窗
const onConfirm = () => {
  // 将临时选择应用到正式选择
  data.selectedList = [...tempSelectedList.value]
  updateDisplayValue()
  updateFormData()

  showSelector.value = false

  // 只有选择了人员才显示提示信息
  if (data.selectedList.length > 0) {
    showToast({
      message: `已选择${data.selectedList.length}名外部人员`,
      type: 'success'
    })
  }
}

// 移除已选择的人员或部门（从临时列表）
const removeSelected = (item) => {
  const index = tempSelectedList.value.findIndex(selected =>
    selected.id === item.id && selected.type === item.type
  )
  if (index > -1) {
    tempSelectedList.value.splice(index, 1)
  }
}

// 监听表单数据变化，初始化显示值
watch(
  () => props.form[config.key],
  (newVal) => {
    if (newVal && Array.isArray(newVal)) {
      data.selectedList = [...newVal]
      updateDisplayValue()
    } else {
      data.selectedList = []
      data.value = ''
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.wrapper-select-outsiders {
  // 基础样式继承自其他yhc组件
  :deep(.van-field__label){
  font-size: 16px !important;
  }
  :deep(.van-field__control){ 
    font-size: 16px !important;
  }
}
.required-field{
  .van-field{
    margin-left: -8px;
  }
}

.outsiders-selector {
  height: 100vh;
  background: #f1f2f6;
  display: flex;
  flex-direction: column;

  /* 移动端滚动优化 */
  -webkit-overflow-scrolling: touch;
  touch-action: manipulation;
}

.search-container {
  // padding: 12px 16px;

  background: #fff;
padding: 0 6px;

  .van-search {
    background: transparent;
    border-radius: 8px !important;
    :deep(.van-search__content) {
      background: #ebecee;
      padding: 0 8px;
border-radius: 8px !important;
      border: none;
      display: flex;
      align-items: center;
      
      .van-field {
        display: flex;
        align-items: center;
        height: 100%;
        padding: 0;
        .van-field__control {
          font-size: 14px;
          color: #323233;
          line-height: 36px;
          // height: 36px;

          input {
            // height: 36px;
            // line-height: 36px;
            border: none;
            outline: none;
            background: transparent;
            font-size: 14px;
            color: #323233;
            width: 100%;

            &::placeholder {
              color: #969799;
              // line-height: 36px;
            }
          }
        }
      }

      .van-icon {
        color: #969799;
        font-size: 16px;
        display: flex;
        align-items: center;
        height: 36px;
      }
    }
  }
}

.address-book-header {
  padding: 12px 16px;
  background: #fff;
  // border-bottom: 1px solid #ebedf0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;

    .title {
      color: #1989fa;
      font-size: 16px;
      font-weight: 500;
    }

    .subtitle {
      color: #323233;
      font-size: 16px;
    }

    .breadcrumb-nav {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .breadcrumb-item {
        display: flex;
        align-items: center;
        color: #323233;
        font-size: 16px;
        cursor: pointer;

        &:not(:last-child) {
          color: #1989fa;
        }

        .breadcrumb-arrow {
          font-size: 12px;
          color: #c8c9cc;
          margin: 0 4px;
        }
      }
    }
  }
}

.breadcrumb-container {
  padding: 8px 16px;
  background: #fff;
  border-bottom: 1px solid #ebedf0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;

  .breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #969799;
    cursor: pointer;

    &:last-child {
      color: #323233;
      font-weight: 500;
    }

    span {
      white-space: nowrap;
    }

    .van-icon {
      font-size: 10px;
      color: #c8c9cc;
    }
  }
}

.select-all-container {
  background: #fff;
  margin: 16px 0;

  .select-all-cell {
    background: #fff;
    border-bottom: 1px solid #ebedf0;
  }
}

.content-container {
  flex: 1;
  overflow-y: auto;
  background: #f1f2f6;
  display: flex;
  flex-direction: column;
}

.custom-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #fff;
  cursor: pointer;
  position: relative;

  &.selected {
    background-color: #f0f9ff;
  }

  &.select-all-cell {
    border-bottom: 1px solid #ebedf0;
  }

  // 部门项样式
  &.department-cell {
    &:not(.last-department)::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 48px; /* 选择图标宽度20px + 右边距12px + 额外间距16px */
      right: 0;
      height: 1px;
      background: #ebedf0;
    }
  }

  // 成员项样式 - 无下边框，较小的上下边距
  &.member-cell {
    padding: 8px 16px;
    // 成员项不显示下边框
  }
}

// 部门列表容器
.department-list {
  background: #fff;
  margin-bottom: 16px; /* 部门和成员之间的间隔，显示为背景色 */
}

// 成员列表容器
.member-list {
  background: #fff;
  // 成员列表延伸到底部
  flex: 1;
  min-height: 300px; // 确保有足够高度
}

  .cell-left {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .cell-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .cell-title {
    font-size: 16px;
    color: #323233;
    font-weight: 400;
  }

  .member-avatar {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    margin-right: 12px;
    flex-shrink: 0;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &.default-avatar {
      background: #1989fa;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .member-info {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .member-position {
      font-size: 16px;
      color: #969799;
    }
  }

  .divider {
    width: 1px;
    height: 16px;
    background: #ebedf0;
  }

  .next-level {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #1989fa;
    font-size: 16px;
    cursor: pointer;

    &.disabled {
      color: #c8c9cc;
      cursor: not-allowed;

      .van-icon {
        color: #c8c9cc;
      }
    }

    .van-icon {
      font-size: 12px;
    }
  }


.select-icon {
  width: 20px;
  height: 20px;
  border: 1px solid #dcdee0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;

  &.selected {
    background: #1989fa;
    border-color: #1989fa;

    .van-icon {
      color: #fff;
      font-size: 12px;
    }
  }
}

.bottom-selected {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 12px 16px;
  border-top: 1px solid #ebedf0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1000;

  .selected-info {
    display: flex;
    align-items: center;
    flex: 1;
    margin-right: 12px;

    span {
      font-size: 14px;
      color: #323233;
      margin-right: 8px;
    }
  }

  .selected-avatars {
    display: flex;
    align-items: center;
    cursor: pointer;

    .avatar-item {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #f0f9ff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 4px;
      border: 1px solid #1989fa;

      .van-icon {
        font-size: 12px;
        color: #1989fa;
      }

      &.department-avatar {
        background: #fff7e6;
        border-color: #ff9500;

        .van-icon {
          color: #ff9500;
        }
      }
    }

    .more-count {
      font-size: 12px;
      color: #969799;
      margin-left: 4px;
    }
  }
}

.selected-detail-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f7f8fa;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fff;
    border-bottom: 1px solid #ebedf0;
    position: sticky;
    top: 0;
    z-index: 10;

    .confirm-btn {
      color: #1989fa;
      font-size: 14px;
      cursor: pointer;
    }
  }

  .selected-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;
    background: #fff;
    margin: 16px 0;
  }

  .selected-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #ebedf0;

    .item-info {
      display: flex;
      align-items: center;
      flex: 1;

      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #f0f9ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: #1989fa;
        font-size: 20px;

        &.department-avatar {
          background: #fff7e6;
          color: #ff9500;
        }

        &.member-avatar-detail {
          border-radius: 8px;
          background: #1989fa;
          color: #fff;
          font-size: 14px;
          font-weight: 500;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .avatar-text {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
          }
        }
      }

      .info-text {
        .name {
          font-size: 16px;
          color: #323233;
          margin-bottom: 2px;
        }

        .position {
          font-size: 14px;
          color: #969799;
          margin-bottom: 2px;
        }

        .department {
          font-size: 12px;
          color: #c8c9cc;
        }
      }
    }
  }

  .popup-footer {
    padding: 16px;
    background: #fff;
    text-align: center;
    border-top: 1px solid #ebedf0;
    cursor: pointer;
    font-size: 16px;
    color: #323233;
  }
}



// 深度选择器，修改vant组件样式
:deep(.van-nav-bar) {
  .van-nav-bar__title {
    font-size: 16px;
    font-weight: 500;
  }

  .van-nav-bar__text {
    font-size: 14px;
  }
}

:deep(.van-cell) {
  .van-cell__title {
    font-size: 14px;
    font-weight: 400;
  }

  .van-cell__value {
    font-size: 12px;
    color: #969799;
  }

  .van-cell__label {
    font-size: 12px;
    color: #969799;
    margin-top: 2px;
  }
}

:deep(.van-search) {
  .van-search__content {
    background: #f7f8fa;
  }
}

:deep(.van-empty) {
  padding: 60px 0;
}
</style>
