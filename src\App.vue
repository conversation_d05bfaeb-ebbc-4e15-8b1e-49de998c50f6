<template>
  <router-view v-slot="{ Component }">
    <transition :name="route.meta.transitionName">
      <component :is="Component" />
    </transition>
  </router-view>
  <yhcTabbar v-if="route.meta.hasTabbar"></yhcTabbar>
</template>
<script setup>

import { onMounted } from "vue";
import { start, close } from "@/untils/nprogress";
import { checkEnv } from "@/untils";
import yhcTabbar from "@/components/yhc-tabbar"
import { useLoginStore } from "@/store/dingLogin";
const app = useLoginStore();
app.browserEnv = checkEnv();
let route = useRoute();
start();
onMounted(() => {
  close();
});
</script>
<style lang="scss">
@use "./assets/styles/init.scss" as *;
@use "./assets/styles/transition.scss" as *;
@use "./assets/styles/base.scss" as *;

#app {
  width: 100%;
  min-height: 100vh;
  background: #F2F3F4;
  border-top: 0.5px solid transparent;
}

</style>
