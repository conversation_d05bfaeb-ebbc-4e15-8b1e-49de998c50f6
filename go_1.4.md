---
description:
globs:
alwaysApply: false
---
# Server 项目开发规范 v1.4

## 1. 项目概述

本文档是 `nebulaserver` 后端项目的核心开发规范，旨在统一团队的开发标准，提高代码质量和可维护性。所有开发人员必须遵循此规范。

## 2. 项目结构

```
/
├── cmd/                    # 命令行入口
├── config/                 # 配置相关
├── doc/                    # 接口文档目录 (按模块分类的 Markdown 文档)
├── internal/               # 内部包 (项目核心代码)
│   ├── controller/         # 控制器层 (Controller)
│   ├── dto/                # 数据传输对象 (DTO)
│   ├── middleware/         # 中间件
│   ├── model/              # 数据模型层 (Model)
│   ├── pkg/                # 内部公共包/工具库
│   ├── router/             # 路由管理
│   └── service/            # 服务层 (Service)
├── proto/                  # Protocol Buffers 定义
├── scripts/                # 脚本文件
├── test/                   # 测试用例目录 (开发调试用，完成后删除)
├── go.mod                  # Go模块文件
├── go.sum                  # Go依赖版本锁定
└── README.md               # 项目高级说明文档
```

## 3. 核心分层架构详解

本项目遵循严格的 **MDC (Model-Service-Controller)** 分层架构，并引入 **DTO (Data Transfer Object)** 层，以实现清晰的职责分离。

-   **Model 层 (`internal/model`)**
    -   **唯一职责**: 定义与数据库表结构 **完全一致** 的 Go 结构体。
    -   所有字段和 `gorm` 标签都直接用于数据库的读写操作。
    -   **严禁** 在此层编写任何业务逻辑、数据转换或数据库查询代码。

-   **DTO 层 (`internal/dto`)**
    -   **唯一职责**: 定义用于 **API 数据传输** 的 Go 结构体。
    -   用于定义请求的 Body 结构和响应的数据结构，是 `Controller` 与外界交互的数据契约。
    -   其结构可以根据 API 的需求灵活设计，可以与 `Model` 完全不同。

-   **Service 层 (`internal/service`)**
    -   **唯一职责**: 存放 **所有业务逻辑**。
    -   负责处理一项或多项业务功能，例如用户注册、订单处理等。
    -   它会调用 `Model` 层与数据库交互，并可能对 `Model` 数据进行处理，最终组装成 `DTO` 或业务结果返回给 `Controller`。

-   **Controller 层 (`internal/controller`)**
    -   **职责**: 作为 HTTP 请求的入口和"胶水层"，保持"轻薄"。
    -   1.  **解析请求**: 从请求中获取参数、查询、Body 等数据。
    -   2.  **调用服务**: 调用一个或多个 `Service` 层的方法来执行业务逻辑。
    -   3.  **返回响应**: 将 `Service` 返回的数据（通常是 `DTO`）封装成统一的 JSON 格式返回给客户端。
    -   **严禁** 在此层编写任何复杂的业务逻辑。

## 4. 命名与开发规范

### 4.1 结构体命名规范 (Struct Naming)

-   **Controller**: 必须以 `Controller` 为后缀。
    -   格式: `type <业务模块>Controller struct`
    -   示例: `type MenuController struct`, `type UserController struct`

-   **Service**: 必须以 `Service` 为后缀。
    -   格式: `type <业务模块>Service struct`
    -   示例: `type MenuService struct`, `type UserService struct`

-   **Model**: 直接使用业务模块的名词，**不加**任何后缀。
    -   格式: `type <业务模块> struct`
    -   示例: `type Menu struct`, `type User struct`

-   **DTO**: 根据其用途以 `DTO` 或具体功能词为后缀。
    -   格式: `type <功能>DTO struct`, `type <功能>Request struct`, `type <功能>Response struct`
    -   示例: `type MenuItemDTO struct`, `type LoginRequest struct`

### 4.2 路由管理规范 (Routing)

#### 4.2.1 路由路径命名规范
-   **强制要求**: 所有路由路径必须使用下划线（_）作为分隔符，**严禁**使用连字符（-）。
-   **路径格式**: `/<module>/<action>`，其中 `module` 和 `action` 都必须使用下划线分隔的小写字母。
-   **正确示例**: `/user_info/get_ls`, `/corp_menu/post_add`, `/system_config/get_info`
-   **错误示例**: `/user-info/get-ls`, `/corpMenu/postAdd`, `/system-config/get_info`

#### 4.2.2 标准路由动作
使用统一的动作命名，并通过 HTTP 方法区分：
-   `GET /<module>/get_ls`: 获取列表 (分页)
-   `GET /<module>/get_all`: 获取全部数据
-   `GET /<module>/get_info`: 获取单条数据 (通常带 `?id=`)
-   `POST /<module>/post_add`: 添加数据
-   `POST /<module>/post_modify`: 修改数据
-   `POST /<module>/post_del`: 删除数据 (通常带 `?id=`)

### 4.3 参数命名全局统一规范 (Parameter Naming)

#### 4.3.1 核心原则
-   **全局一致性**: 相同含义的参数在整个项目中必须使用完全相同的名称，包括：
    -   API 接口参数（URL 参数、查询参数、请求体参数）
    -   数据库字段名
    -   Go 结构体字段名
    -   配置文件参数名

#### 4.3.2 命名规则
-   **统一格式**: 必须使用下划线分隔的小写命名（snake_case）
-   **禁止变体**: 同一概念严禁使用多种命名方式
-   **示例对比**:
    ```go
    // ✅ 正确 - 全项目统一使用
    corp_id      // 企业ID
    user_id      // 用户ID
    menu_id      // 菜单ID
    created_at   // 创建时间
    updated_at   // 更新时间

    // ❌ 错误 - 禁止的变体形式
    corpId, corpid, CorpId, CORP_ID
    userId, userid, UserId, USER_ID
    menuId, menuid, MenuId, MENU_ID
    createdAt, createdat, CreatedAt
    updatedAt, updatedat, UpdatedAt
    ```

#### 4.3.3 标准参数命名约定
-   **ID 类参数**: 统一使用 `<实体>_id` 格式
    -   `corp_id` - 企业ID
    -   `user_id` - 用户ID
    -   `menu_id` - 菜单ID
    -   `role_id` - 角色ID
    -   `dept_id` - 部门ID
-   **时间类参数**:
    -   `created_at` - 创建时间
    -   `updated_at` - 更新时间
    -   `deleted_at` - 删除时间
    -   `start_time` - 开始时间
    -   `end_time` - 结束时间
-   **状态类参数**:
    -   `is_active` - 是否激活
    -   `is_deleted` - 是否删除
    -   `is_enabled` - 是否启用
-   **分页参数**:
    -   `page_num` - 页码
    -   `page_size` - 每页大小
    -   `total_count` - 总数量

#### 4.3.4 检查与执行
-   **代码审查**: 所有 PR 必须检查参数命名一致性
-   **数据库设计**: 新建表时必须遵循统一命名规范
-   **API 设计**: 新增接口时必须使用标准参数名
-   **重构要求**: 发现不一致命名时必须及时重构统一

### 4.4 接口响应规范 (API Response)

-   **统一响应格式**:
    ```json
    {
      "status": 0,      // 状态码: 0-成功，非0-失败
      "msg": "",        // 错误信息 (失败时必须有值)
      "data": {}        // 响应数据 (成功时返回)
    }
    ```
-   **分页响应格式**: `data` 字段内必须包含 `items` 和 `total`。
    ```json
    {
      "status": 0,
      "msg": "",
      "data": {
        "items": [],    // 数据内容数组
        "total": 100    // 数据总数
      }
    }
    ```

### 4.5 代码风格 (Code Style)
-   严格遵循 Go 官方代码规范，提交前必须使用 `gofmt` 或 `goimports` 格式化代码。
-   使用 `golangci-lint` 进行代码检查。
-   公开的函数、方法、重要变量必须添加清晰的中文注释。
-   单行代码长度建议不超过 `120` 字符。
-   单个函数长度建议不超过 `50` 行（不含注释和空行）。
-   包名使用小写、简洁的名词。
-   **命名一致性检查**: 所有变量、函数、结构体字段必须遵循本规范的命名约定。

## 5. 数据库规范 (Database)
-   主数据库为 MySQL，ORM 框架为 GORM。
-   所有写操作（Create, Update, Delete）必须在 **Service 层** 使用事务。
-   **表名**: 全小写，使用下划线分隔 (e.g., `user_accounts`, `corp_menus`)。
-   **字段名**: 全小写，使用下划线分隔，必须与 API 参数名保持一致 (e.g., `user_name`, `corp_id`)。
-   **标准字段**: 所有表必须包含 `id`, `created_at`, `updated_at`, `deleted_at` 字段 (可通过继承 `BaseModel` 实现)。
-   **外键命名**: 外键字段必须使用 `<关联表单数>_id` 格式 (e.g., `user_id`, `corp_id`, `menu_id`)。
-   **命名一致性**: 数据库字段名必须与对应的 Go 结构体字段名（转换为 snake_case 后）完全一致。

## 6. 测试用例管理规范 (Test Case Management)

### 6.1 测试目录结构
-   **统一位置**: 所有测试用例必须放在根目录下的 `test/` 目录中。
-   **目录结构**:
    ```
    test/
    ├── controller/         # Controller 层测试
    ├── service/            # Service 层测试
    ├── model/              # Model 层测试
    ├── integration/        # 集成测试
    ├── mock/               # Mock 数据和工具
    └── fixtures/           # 测试数据文件
    ```

### 6.2 测试文件命名规范
-   **单元测试**: `<模块名>_test.go`
-   **集成测试**: `<功能模块>_integration_test.go`
-   **基准测试**: `<模块名>_benchmark_test.go`

### 6.3 测试生命周期管理
-   **开发阶段**: 在 `test/` 目录下创建必要的测试用例进行功能验证。
-   **测试完成**: 功能开发和测试完成后，**必须删除** `test/` 目录下的临时测试文件。
-   **保留规则**: 只保留核心功能的关键测试用例，其他调试性测试文件一律删除。
-   **项目整洁**: 确保提交到版本控制的代码库保持整洁，避免冗余测试文件。

## 7. 接口文档管理规范 (API Documentation)

### 7.1 文档目录结构
-   **统一位置**: 所有接口文档必须放在根目录下的 `doc/` 目录中。
-   **按模块分类**: 每个业务模块对应一个独立的 Markdown 文档文件。
-   **目录结构**:
    ```
    doc/
    ├── user.md             # 用户相关接口
    ├── menu.md             # 菜单相关接口
    ├── corp.md             # 企业相关接口
    ├── auth.md             # 认证相关接口
    ├── system.md           # 系统配置接口
    └── README.md           # 接口文档总览
    ```

### 7.2 文档格式规范
-   **文件格式**: 统一使用 Markdown 格式 (`.md`)。
-   **文档结构**: 每个模块文档必须包含以下部分：
    ```markdown
    # <模块名>接口文档

    ## 概述
    模块功能描述

    ## 接口列表

    ### 1. 接口名称
    - **请求方式**: GET/POST
    - **请求路径**: /module/action
    - **功能描述**: 接口功能说明
    - **请求参数**:
      | 参数名 | 类型 | 必填 | 说明 |
      |--------|------|------|------|
      | param1 | string | 是 | 参数说明 |
    - **响应示例**:
      ```json
      {
        "status": 0,
        "msg": "",
        "data": {}
      }
      ```
    ```

### 7.3 文档同步要求
-   **强制同步**: 每次更新接口后，**必须同步更新**对应模块的接口文档。
-   **更新内容**: 包括但不限于：
    -   新增接口的完整文档
    -   修改接口的参数变更
    -   删除接口的标记说明
    -   响应格式的变化
-   **审查要求**: 代码审查时必须检查接口文档是否已同步更新。
-   **版本一致性**: 确保文档内容与实际接口实现保持完全一致。

## 8. 日志与错误处理 (Logging & Errors)
-   日志统一使用 `internal/pkg/logger`。
-   日志格式为 JSON，方便机器解析。
-   **错误必须在发生的最低层级记录日志**。例如，Service 层数据库操作失败，应立即在 Service 层记录日志，Controller 层只需处理并返回错误响应，无需重复记录。
-   关键操作（如登录、支付、重要数据修改）必须记录日志。

## 9. 规范执行与检查机制 (Compliance & Validation)

### 9.1 强制性检查项目
-   **路由命名检查**: 所有路由路径必须使用下划线分隔，禁止连字符
-   **参数命名检查**: 相同概念的参数在全项目中必须使用统一命名
-   **数据库字段检查**: 字段名必须与 API 参数名保持一致
-   **结构体字段检查**: Go 结构体字段名转换为 snake_case 后必须与数据库字段名一致
-   **测试文件检查**: 确保测试完成后及时清理 `test/` 目录
-   **文档同步检查**: 接口更新后必须同步更新 `doc/` 目录下的对应文档

### 9.2 代码审查要求
-   **PR 审查**: 每个 Pull Request 必须检查以下内容：
    -   命名规范一致性
    -   测试文件清理情况
    -   接口文档同步状态
-   **新功能开发**: 新增 API、数据表、结构体时必须遵循统一命名规范
-   **重构义务**: 发现不符合规范的命名时，必须在相关功能开发中一并重构
-   **文档完整性**: 新增或修改接口时必须提供完整的接口文档

### 9.3 工具辅助检查
-   建议使用 linter 工具检查命名规范一致性
-   数据库迁移脚本必须经过命名规范审查
-   API 文档生成时自动检查参数命名一致性
-   定期检查 `test/` 目录是否存在遗留的测试文件
-   使用脚本工具验证接口文档与实际接口的一致性

### 9.4 违规处理
-   **轻微违规**: 要求立即修正，不影响功能合并
-   **严重违规**: 拒绝合并，要求全面整改后重新提交
-   **历史遗留**: 制定专项重构计划，逐步统一历史代码
-   **文档缺失**: 接口文档不完整或不同步的，必须补充完整后才能合并
-   **测试文件遗留**: 发现 `test/` 目录有遗留文件的，必须清理后才能合并
