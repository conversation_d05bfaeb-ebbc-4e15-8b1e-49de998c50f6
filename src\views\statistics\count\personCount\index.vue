<template>
  <div class="statistics-page">
    <!-- 关键数据统计组件 -->
    <KeyDataStats />

    <!-- 趋势统计组件 -->
    <TrendStats />

    <!-- 场所占比组件 -->
    <VenueStats />

    <!-- 餐时占比组件 -->
    <MealtimeStats />

    <!-- 档口占比组件 -->
    <StallStats />

    <!-- 人流量统计热力图组件 -->
    <HeatmapStats />
  </div>
</template>

<script setup>
import KeyDataStats from './components/KeyDataStats.vue'
import TrendStats from './components/TrendStats.vue'
import VenueStats from './components/VenueStats.vue'
import MealtimeStats from './components/MealtimeStats.vue'
import StallStats from './components/StallStats.vue'
import HeatmapStats from './components/HeatmapStats.vue'
</script>

<style lang="scss" scoped>
.statistics-page {
  min-height: 100vh;
  background-color: #f2f3f4;
}
</style>