<template>
  <div class="wrapper">
    <yhc-form ref="yhcFormRef" :config="config" pageType="add"@onSubmit="onFormSubmit" />
  </div>
</template>
<script setup>
import { ref, onMounted, reactive, nextTick, computed } from "vue";
import { getCurrentInstance } from 'vue'
import { useRoute } from 'vue-router'
const { proxy } = getCurrentInstance();
const route = useRoute();
const dininghall = localStorage.getItem("dininghall");
//将dininghall数据格式换成int类型
const dininghall_id = parseInt(dininghall);
// 获取yhc-form组件的引用
const yhcFormRef = ref(null);
const stall= ref(false);
// 创建一个独立的ref来控制disabled状态
const stallDisabled = ref(false);
// 初始化postData
const postData = reactive({
  dininghall_id: dininghall_id,
  // 如果有id参数，确保id是整数类型
  ...(route.query.id ? { id: parseInt(route.query.id) } : {})
});

const clearOnlineBookCache = () => {
  try {
    // 清除onlineBook页面的表单缓存
    const cacheKey = 'onlineBook__consumeRuleOnlineBook_new';
    localStorage.removeItem(cacheKey);

    // 如果是编辑模式（有id参数），清除详情数据缓存
    if (route.query.id) {
      const detailCacheKey = `consumeRule_detail_${route.query.id}`;
      localStorage.removeItem(detailCacheKey);
      console.log('add页面提交成功，已清除详情数据缓存，id:', route.query.id);
    }
  } catch (error) {
    // 清除缓存失败
    console.error('清除缓存失败:', error);
  }
};

const onFormSubmit = (formData) => {
  console.log('add页面onFormSubmit接收到的formData:', formData);
  console.log('add页面当前的config.postData:', config.postData);

  // 🔧 最优先修复：立即修正formData中的id类型
  if (formData.id && typeof formData.id === 'string') {
    formData.id = parseInt(formData.id);
    console.log('add页面强制修正formData.id类型:', formData.id, typeof formData.id);
  }

  // 🔧 关键修复：将config.postData中的最新数据同步到formData中
  // 确保yhc-form提交时使用的是最新的线上预订数据
  Object.keys(config.postData).forEach(key => {
    if (key !== 'dininghall_id') {
      // 🔧 特殊处理id字段，确保是整数类型
      if (key === 'id' && config.postData[key]) {
        formData[key] = parseInt(config.postData[key]);
      } else {
        formData[key] = config.postData[key];
      }
    }
  });

  // 判断是否有线上预订数据
  const hasOnlineBookingData = Object.keys(config.postData).some(key =>
    key !== 'dininghall_id' &&
    key !== 'online_booking_enabled' &&
    config.postData[key] !== undefined &&
    config.postData[key] !== '' &&
    !(Array.isArray(config.postData[key]) && config.postData[key].length === 0)
  );

  // 设置线上预订开关
  const onlineBookingEnabled = hasOnlineBookingData ? 1 : 0;
  config.postData.online_booking_enabled = onlineBookingEnabled;
  formData.online_booking_enabled = onlineBookingEnabled;

  // 🔧 修复id类型问题：确保id是整数类型
  if (route.query.id) {
    const id = parseInt(route.query.id);
    config.postData.id = id;
    formData.id = id;
  }

  // 🔧 最终检查：确保formData中的id是整数类型
  if (formData.id && typeof formData.id === 'string') {
    formData.id = parseInt(formData.id);
  }

  console.log('add页面同步后的formData:', formData);
  console.log('add页面最终的config.postData:', config.postData);
  clearOnlineBookCache();
};

onMounted(async () => {
  console.log('add页面 route.query:', route.query);
  console.log('add页面初始postData:', postData);

  // 🔧 关键修复：等待yhc-form组件初始化完成后，修正config.postData中的id类型
  // 因为yhc-form会将route.query合并到config.postData中，覆盖我们设置的整数类型id
  await nextTick();
  if (route.query.id) {
    const intId = parseInt(route.query.id);

    // 修正config.postData中的id
    config.postData.id = intId;

    // 🔧 最关键：修正yhc-form内部form对象中的id
    // 这是yhc-form实际用于API调用的数据源
    if (yhcFormRef.value && yhcFormRef.value.form) {
      yhcFormRef.value.form.id = intId;
      console.log('add页面修正后的yhc-form.form.id:', yhcFormRef.value.form.id, typeof yhcFormRef.value.form.id);
    }

    console.log('add页面修正后的config.postData.id:', config.postData.id, typeof config.postData.id);
  }

  // 🔧 额外保险：监听yhc-form的数据变化，确保id始终是整数类型
  if (yhcFormRef.value && yhcFormRef.value.form) {
    const originalForm = yhcFormRef.value.form;
    // 使用Proxy监听form对象的变化
    const formProxy = new Proxy(originalForm, {
      set(target, prop, value) {
        if (prop === 'id' && typeof value === 'string') {
          target[prop] = parseInt(value);
          console.log('add页面Proxy拦截并修正id类型:', target[prop], typeof target[prop]);
        } else {
          target[prop] = value;
        }
        return true;
      }
    });
    // 注意：这里不能直接替换form对象，因为Vue的响应式系统
  }

  // 处理从onlineBook页面返回的数据
  const consumeRuleFormData = localStorage.getItem('consumeRuleFormData');
  if (consumeRuleFormData) {
    try {
      const onlineBookData = JSON.parse(consumeRuleFormData);
      console.log('add页面接收到onlineBook传递的数据:', onlineBookData);

      // 直接将onlineBook处理好的数据合并到postData中
      Object.assign(config.postData, onlineBookData);

      // 🔧 关键修复：同步数据到yhc-form的内部form对象
      await nextTick();
      if (yhcFormRef.value && yhcFormRef.value.form) {
        console.log('add页面更新前的yhc-form.form:', yhcFormRef.value.form);

        // 将onlineBook的数据同步到yhc-form的form对象中
        Object.keys(onlineBookData).forEach(key => {
          yhcFormRef.value.form[key] = onlineBookData[key];
        });

        console.log('add页面更新后的yhc-form.form:', yhcFormRef.value.form);
      }

      // 清除传递数据，避免重复使用
      localStorage.removeItem('consumeRuleFormData');
      console.log('add页面已合并onlineBook数据到postData和form');
    } catch (error) {
      console.error('解析onlineBook数据失败:', error);
    }
  }

  // 获取档口配置，控制适用档口的disabled状态
  await getStallConfig();
});

// 创建config对象
const config = reactive({
  curl: {
    add: "/consumption_rule/post_add",
    info: "/consumption_rule/get_info",
    edit: "/consumption_rule/post_modify",
  },
  postData: postData,

  // 🔧 关键修复：确保API返回的数据中id字段是整数类型
  format: (res) => {
    if (res && res.id) {
      res.id = parseInt(res.id);
    }
    return res;
  },

  search: {},

  groupForm: [
    [0, 1],
    [1, 2],
    [2, 3],
    [3, 4],
    [4, 5],
    [5, 7],
    [7, 8],
    [8, 9],
    [9, 10],
    [10, 11],
    [11, 12],
    [12, 13],
    [13, 14],
    [14, 15],
    [15, 17],
  ],
  form: [
    {
      label: "规则名称",
      key: "title",
      required: true,
      component: "yhc-input",
      rules: [{ required: true, message: "请填写规则名称" }]
    },
    {
      label: "临时方案",
      key: "is_temporary",
      component: "yhc-switch",
      rules: [{ required: false, message: "临时方案" }],
      child: {
        showMode: false,
        formChild: [
          {
            label: "失效时间",
            type: "datetime",
            required: true,
            key: "expire_time",
            component: "yhc-picker-date",
            rules: [{ required: true, message: "请填写截止时间" }],
          },
        ],
      },
    },
    {
        label:"适用人员",
        key: "apply_personnel_type",
        component: "yhc-radio-group",
        rules: [{ required: true, message: "请选择适用人员" }],
        options: [
          { value: 0, label: "全部人员" },
          { value: 1, label: "指定人员" }
        ],
        shape:"dot",
        defaultValue: 0,
        child: {
        showMode: 1, // 当选中"指定人员"时显示子表单
        form: [
          {
            label: "内部员工",
            key: "internal_staffs",
            component: "yhc-select-user",
          },
           {
            label: "外部员工",
            key: "external_staffs",
            component: "yhc-select-user",
          }
        ]
      }
    },
    {
        label:"适用餐时",
        key: "apply_mealtime_type",
        component: "yhc-radio-group",
        rules: [{ required: true, message: "请选择适用餐时" }],
        options: [
          { value: 0, label: "全部餐时" },
          { value: 1, label: "指定餐时" }
        ],
        shape:"dot",
        defaultValue: 0,
        child: {
        showMode: 1, // 当选中"指定餐时"时显示子表单
        form: [
          {
            label: "指定餐时",
            key: "mealtimes",
            component: "yhc-picker",
            required: true,
            rules: [{ required: true, message: "请选择餐时" }],
            opts: {
              url: "/mealtime/get_all",
              postData: {},
              merge: false,
              multiple: true,
              text_key: "title",
              contrast_key: "id",
              keyMap: [{ title: "title", id: "id" }],
              defaultList: [],
              card:[
                {
                  title: "title",
                  slotMap: {
                    desc: [
                      {
                        title: "开始时间：",
                        key: "start_time",
                      },
                      {
                        title: "结束时间：",
                        key: "end_time",
                      },
                    ],
                  },
                },
              ]
            },
            popup: {
              round: true,
              position: "bottom",
              style: { height: "50vh", overflow: "hidden" },
              closeable: false,
            },


          },

        ]
      }
    },
    {
        label:"适用档口",
        key: "apply_stall_type",
        component: "yhc-radio-group",
        disabled: stallDisabled,
        rules: [{ required: true, message: "请选择适用档口" }],
        options: [
          { value: 0, label: "全部档口" },
          { value: 1, label: "指定档口" }
        ],
        shape:"dot",
        defaultValue: 0,
        child: {
        showMode: 1, // 当选中"指定档口"时显示子表单
        form: [
          {
            label: "指定档口",
            key: "stalls",
            required: true,
            component: "yhc-picker",
            rules: [{ required: true, message: "请选择档口" }],
            opts: {
              url: "/stall/get_all",
              postData: {},
              merge: false,
              multiple: true,
              text_key: "title",
              contrast_key: "id",
              keyMap: [{ title: "title", id: "id" }],
              defaultList: [],
            },
            popup: {
              round: true,
              position: "bottom",
              style: { height: "50vh", overflow: "hidden" },
              closeable: false,
            },
          },

        ]
      }
    },
    {
      label: "考勤规则",
      key: "attendance_rule",
      component: "yhc-picker",
      default: {},
      // rules: [{ required: true, message: "请选择考勤规则" }],
      opts: {
        url: "/attendance_rule/get_all",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "rule_name",
        contrast_key: "id",
        keyMap: { title: "rule_name", id: "id" },
        defaultList: [],
      },
      addButton: {
        show: true,
        text: "添加考勤规则",
        icon: "plus",
        type: "primary",
        routeUrl: "/systemConfig/attendanceRuleAdd",
      },
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false,
      },
      
    },
    {
      label: "减免规则",
      key: "discount_rule",
      component: "yhc-picker",
      default:{},
      // rules: [{ required: true, message: "请选择减免规则" }],
      opts: {
        url: "/discount_rule/get_all",
        postData: {},
        merge: false,
        multiple: false,
        text_key: "rule_name",
        contrast_key: "id",
        keyMap: { title: "rule_name", id: "id" },
        defaultList: [],
      },
      addButton:{
        show: true,
        text: "添加减免规则",
        icon: "plus",
        type: "primary",
        routeUrl:"/systemConfig/reduceRuleAdd"
      },
      popup: {
        round: true,
        position: "bottom",
        style: { height: "50vh", overflow: "hidden" },
        closeable: false,
      },
      card: {
        slotMap: {
          desc: [
            {
              title: "说明：",
              key: "detail",
            },
          ],
        },
        title: "title",
      },
    },
    {
        label:"每餐消费次数",
        key: "consumption_times_per_meal",
        component: "yhc-stepper",
        required: true,
        rules: [{ required: false, message: "请填写每餐消费次数" }],
    },
    {
        label:"消费金额上限",
        key: "amount_limit_enabled",
        component: "yhc-radio-group",
        rules: [{ required: false, message: "限制消费" }],
        options: [
          { value: 0, label: "无上限" },
          { value: 1, label: "自定义上限" }
        ],
        shape:"dot",
        defaultValue: 0,
        child: {
        showMode: 1, // 当选中"自定义上限"时显示子表单
        form: [
          {
            label: "上限金额",
            key: "amount_limit",
            component: "yhc-input",
            required: true,
            type:"number",
            rules: [{ required: true, message: "请输入限额金额" }],
            "right-icon": "元",
            decimalPlaces: true,
            min: 1
          },
          {
            label:"统计范围",
            component: "yhc-radio-group",
            key: "amount_limit_scope",
            shape:"dot",
            defaultValue: 0,
            options: [
              {
                label: "当前餐厅",
                value: 0
              },
              {
                label: "全部场所",
                value: 1
              }
            ]
          },
          {
            label:"统计周期",
            component:"yhc-picker",
            default: 0,
            key: "amount_limit_period",
            opts: {
              url: "",
              postData: {},
              merge: false,
              multiple: false,
              text_key: "label",
              contrast_key: "value",
              keyMap: "value",
              defaultList: [
                {
                  label: "每餐",
                  value: 0
                },
                {
                  label: "每天",
                  value: 1
                },
                {
                  label: "每周",
                  value: 2
                },
                {
                  label: "每月",
                  value: 3
                }
              ]
            },
            popup: {
              round: true,
              position: "bottom",
              style: { height: "50vh", overflow: "hidden" },
              closeable: false,
            }
          }
        ]
      }

    },
    {
        label:"单次消费加价",
        key: "single_consumption_markup",
        component: "yhc-input",
        required: true,
        labelWidth:110,
        type: "number",
        decimalPlaces: true,
        "right-icon": "元",
        rules: [{ required: false, message: "限制消费" }],
    },
    {
      label:"例：填1元，每次消费加价1元",
      component: "yhc-desc"
    },
    {
        label:"线上预订",
        key:"online",
        component:"yhc-select-nav",
        routeUrl:"/consumeRuleOnlineBook",
        // 根据是否有id参数来判断是否从detail页面跳转（编辑模式）
        routeQuery: (() => {
          console.log('add页面计算routeQuery时的route.query:', route.query);
          // 如果有id参数，说明是编辑模式（从detail页面跳转）
          const isEditMode = route.query.id;
          console.log('add页面检查是否为编辑模式:', isEditMode);

          const query = isEditMode ?
            { id: route.query.id, from: 'detail' } : {}
          console.log('add页面yhc-select-nav routeQuery:', query)
          return query
        })()
    }
  ]
});

const getStallConfig = async () => {
  //get请求调取接口
  const res = await proxy.$get('/dininghall/get_info', {id:dininghall_id})
  if (res.code === 200) {
    const shouldDisable = res.data.merge_stall == 1;

    // 更新响应式变量
    stall.value = shouldDisable;
    stallDisabled.value = shouldDisable;

    // 直接更新表单配置中的disabled值
    const stallFormItem = config.form.find(item => item.key === 'apply_stall_type');
    if (stallFormItem) {
      stallFormItem.disabled = shouldDisable;
    }

    // 强制更新组件以确保UI反映最新状态
    await nextTick();
    if (yhcFormRef.value) {
      yhcFormRef.value.$forceUpdate();
    }
  }
}
const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: route.query.id ? '修改消费规则' : '新增消费规则',
  });
};
setRightA()
setRight()
</script>
<style lang="scss" scoped>
.wrapper {
  width: 100%;
  min-height: calc(100vh - 1px);
  border-top: 1px solid #f2f3f4;
}
</style>
