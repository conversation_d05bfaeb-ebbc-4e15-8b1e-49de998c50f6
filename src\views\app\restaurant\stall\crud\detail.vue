<template>
  <div class="home-container">
    <!-- yhc-form 组件展示 -->
    <yhc-form
      :config="basicFormConfig"
      pageType="detail"
      :editRedirectConfig="editRedirectConfig"
    />
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue'
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const { proxy } = getCurrentInstance();
const route = useRoute()

// 基础表单配置
const basicFormConfig = {
  button: {
    isShow: true,
  },
  postData: {
    id: route.query.id // 从路由获取id参数
  },
  curl: {
    info: '/stall/get_info', // 获取详情接口
    del: '/stall/post_del' // 删除接口
  },
  groupForm: [
    [0, 1],
    [1, 4]
  ],
  form: [
    {
      label: "档口名称",
      key: "title",
      component: "yhc-input",
      type: "text",
      disabled: true,
      placeholder: "请输入",
      required: true,
      rules: [{ required: true, message: "请填写用户名" }],
    },
    {
      label: "消费机",
      key: "device_consumptions",
      component: "yhc-picker",
      placeholder: "请选择",
      disabled: true,
      ellipsis:32,
      opts: {
        url: "/device/get_device_all",
        postData: {type:1},
        merge: false,
        multiple: true,
        text_key: "title",
        contrast_key: "id",
        keyMap: [{ id: "id", title: "title" }],
        defaultList: []
      },
    },
    {
      label: "收银机",
      key: "device_cashiers",
      component: "yhc-picker",
      placeholder: "请选择",
      disabled: true,
      card: {
        title: "title",
      },
      opts: {
        url: "/device/get_device_all",
        postData: {type: 0},
        merge: false,
        multiple: false,
        text_key: "title",
        contrast_key: "id",
        keyMap: { id: "id", title: "title" },
        defaultList: []
      },
    },
  ]
}

// 修改按钮跳转配置
const editRedirectConfig = {
  path: '/stallAdd', // 跳转到新增页面进行编辑
  query: {
    id: route.query.id, // 传递id参数
    from: 'detail' // 标识来源
  }
}

const setRight = () => {
    proxy.$_dd.biz.navigation.setRight({
        text:'',
        control: true,
        onSuccess: function () {
        },
        onFail: function () { },
    });
};
const setRightA = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: '档口详情',
  });
};
setRightA()
setRight()
</script>

<style lang="scss" scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  // background: #f7f8fa;
}
</style>
