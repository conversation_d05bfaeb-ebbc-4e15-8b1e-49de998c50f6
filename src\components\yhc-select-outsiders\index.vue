<template>
  <div class="yhc-select-outsiders" :class="{ 'required-cont': config.required }">
    <van-field
      v-model="data.value"
      :label="config.label"
      :placeholder="config.placeholder"
      :required="config.required"
      :disabled="config.disabled"
      readonly
      is-link
      input-align="left"
      @click="onClick"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { deepAssign } from "@/untils"

// 路由实例
const router = useRouter()

// 默认配置
let config = {
  label: "外部人员",
  key: "outsiders",
  type: "text",
  placeholder: "请选择外部人员",
  required: false,
  disabled: false,
  rules: [],
}

// Props定义
const props = defineProps({
  config: {
    type: Object,
    default: () => ({})
  },
  form: {
    type: Object,
    required: true
  }
})

// 合并配置
config = deepAssign(config, props.config)

// 响应式数据
const data = reactive({
  value: "",
  selectedList: [], // 已选择的人员列表
})

// 点击输入框跳转到选择页面
const onClick = () => {
  if (config.disabled) return
  
  // 将当前选择数据存储到路由参数中
  const currentData = {
    selectedList: data.selectedList,
    formKey: config.key
  }
  
  // 跳转到外部人员选择页面
  router.push({
    path: '/select-outsiders',
    query: {
      data: JSON.stringify(currentData),
      returnPath: router.currentRoute.value.fullPath
    }
  })
}

// 更新显示值
const updateDisplayValue = () => {
  if (data.selectedList.length === 0) {
    data.value = ''
    return
  }
  
  const departments = data.selectedList.filter(item => item.type === 'department')
  const members = data.selectedList.filter(item => item.type === 'member')
  
  const deptCount = departments.length
  const memberCount = members.length
  
  if (deptCount > 0 && memberCount > 0) {
    data.value = `选择${deptCount}部门${memberCount}员工`
  } else if (deptCount > 0) {
    data.value = `选择${deptCount}部门`
  } else if (memberCount > 0) {
    data.value = `选择${memberCount}员工`
  }
}

// 更新表单数据
const updateFormData = () => {
  if (props.form && config.key) {
    props.form[config.key] = data.selectedList
  }
}

// 监听全局选择结果
const checkForUpdates = () => {
  if (window.selectedOutsiders && window.selectedOutsiders.key === config.key) {
    data.selectedList = window.selectedOutsiders.data
    updateDisplayValue()
    updateFormData()
    // 清除全局数据
    delete window.selectedOutsiders
  }
}

// 组件挂载
onMounted(() => {
  // 初始化数据
  if (props.form[config.key] && Array.isArray(props.form[config.key])) {
    data.selectedList = [...props.form[config.key]]
    updateDisplayValue()
  }
  
  // 页面获得焦点时检查更新
  window.addEventListener('focus', checkForUpdates)
  
  // 立即检查一次（防止页面刷新后丢失数据）
  setTimeout(checkForUpdates, 100)
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('focus', checkForUpdates)
})

// 监听表单数据变化，初始化显示值
import { watch } from 'vue'
watch(
  () => props.form[config.key],
  (newVal) => {
    if (newVal && Array.isArray(newVal)) {
      data.selectedList = [...newVal]
      updateDisplayValue()
    } else {
      data.selectedList = []
      data.value = ''
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.yhc-select-outsiders {
  // 基础样式

  &.required-cont {
    :deep(.van-field) {
      margin-left: -8px;
    }
  }
}
</style>
