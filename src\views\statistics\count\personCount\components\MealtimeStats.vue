<template>
  <div class="venue-stats">
    <!-- 场所占比整体区域 -->
    <div class="venue-section">
      <!-- 头部区域 -->
      <div class="header-section">
        <!-- 标题行 -->
        <div class="header-top">
          <div class="venue-title">餐时占比</div>
          <div class="header-right">
            <!-- 筛选按钮 -->
            <div class="filter-selector" @click="openFilterPopup">
              <span class="filter-text">筛选</span>
              <van-icon name="play" size="12" color="#C8C9CC" style="transform: rotate(90deg);" />
            </div>
          </div>
        </div>

        <!-- 已选择筛选条件 -->
        <div class="filter-tags" v-if="hasActiveFilters">
          <span>已选择：</span>
          <span>{{ activeFiltersText }}</span>
        </div>
      </div>

      <!-- 图表整体区域 -->
      <div class="chart-wrapper">
        <!-- 图例 -->
        <div class="chart-legend">
          <div class="legend-item" v-for="item in chartData" :key="item.name">
            <div class="legend-dot" :style="{ background: item.color }"></div>
            <span>{{ item.name }}</span>
          </div>
        </div>

        <!-- ECharts 饼图容器 -->
        <div class="chart-container">
          <div ref="chartRef" class="echarts-chart"></div>
        </div>
      </div>
    </div>

    <!-- 筛选弹窗 -->
    <van-popup
      v-model:show="showFilterPopup"
      round
      position="bottom"
      :style="{ height: '70vh' }"
    >
      <div class="filter-popup">
        <!-- {{ AURA-X: Modify - 重新设计为表单样式的筛选弹窗 }} -->
        <div class="filter-header">
          <span class="filter-title">筛选条件</span>
        </div>

        <!-- {{ AURA-X: Add - 添加左右分栏布局 }} -->
        <div class="filter-body">
          <!-- 左侧导航菜单 -->
          <div class="filter-nav">
            <div
              class="filter-nav-item"
              :class="{
                active: activeNavItem === 'date',
                'has-selection': selectedFilters.startDate !== '' || selectedFilters.endDate !== ''
              }"
              @click="scrollToSection('date')"
            >
              <span class="nav-indicator" :class="{ 'has-data': selectedFilters.startDate !== '' || selectedFilters.endDate !== '' }"></span>
              <span class="nav-text">日期</span>
            </div>
            <div
              class="filter-nav-item"
              :class="{
                active: activeNavItem === 'dininghall',
                'has-selection': selectedFilters.dininghall !== null
              }"
              @click="scrollToSection('dininghall')"
            >
              <span class="nav-indicator" :class="{ 'has-data': selectedFilters.dininghall !== null }"></span>
              <span class="nav-text">场所</span>
            </div>
          </div>

          <!-- 右侧筛选内容 -->
          <div class="filter-content" ref="filterContentRef">
            <!-- 开始时间筛选 -->
            <div class="filter-form-item" ref="startDateRef">
              <div class="filter-label">
                <span class="label-text">日期</span>
              </div>
              <div class="filter-value">
                <van-field
                  v-model="selectedFilters.startDate"
                  is-link
                  readonly
                  left-icon="clock-o"
                  placeholder="请选择开始时间"
              
                  @click="openDatePicker('start')"
                />
              </div>
               <div class="filter-value">
                <van-field
                  v-model="selectedFilters.startDate"
                  is-link
                  readonly
                  left-icon="clock-o"
                  placeholder="请选择开始时间"
                  @click="openDatePicker('start')"
                />
              </div>
            </div>
            <!-- 场所筛选 -->
            <div class="filter-form-item" ref="dininghallRef">
              <div class="filter-label">
                <span class="label-text">场所</span>
              </div>
              <div class="filter-value">
                <div class="filter-select-options">
                  <div
                    v-for="dininghall in dininghallOptions"
                    :key="dininghall.id"
                    class="filter-select-option"
                    :class="{ active: selectedFilters.dininghall === dininghall.id }"
                    @click="selectFilter('dininghall', dininghall.id)"
                  >
                    {{ dininghall.title }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- {{ AURA-X: Modify - 重新设计底部按钮样式 }} -->
        <div class="filter-footer">
          <div class="filter-button reset-button" @click="onFilterReset">
            重置
          </div>
          <div class="filter-button confirm-button" @click="onFilterConfirm">
            确定
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom" round>
      <van-date-picker
        v-model="currentDate"
        title="选择日期"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, getCurrentInstance } from 'vue'
import { showToast } from 'vant'
import * as echarts from 'echarts'

const { proxy } = getCurrentInstance()

// 筛选弹窗相关
const showFilterPopup = ref(false)
const activeNavItem = ref('date')
const filterContentRef = ref(null)
const startDateRef = ref(null)
const endDateRef = ref(null)
const dininghallRef = ref(null)

// 日期选择器相关
const showDatePicker = ref(false)
const currentDate = ref(['2025', '07', '23'])
const currentDateType = ref('start') // 'start' 或 'end'

// ECharts 相关
const chartRef = ref(null)
let chartInstance = null

// 图表数据
const chartData = ref([
  { name: '宵夜', value: 18, color: '#6366F1' },
  { name: '早餐', value: 67, color: '#06B6D4' },
  { name: '晚餐', value: 15, color: '#EC4899' }
])

// 场所选项数据
const dininghallOptions = ref([])

// 筛选条件 - 当前选择状态（弹窗内的临时状态）
const selectedFilters = reactive({
  startDate: '2025-07-23',     // 开始日期
  endDate: '2025-07-24',       // 结束日期
  dininghall: null             // 场所ID
})

// 确认后的筛选状态，用于控制页面显示
const confirmedFilters = reactive({
  startDate: '2025-07-23',     // 确认后的开始日期
  endDate: '2025-07-24',       // 确认后的结束日期
  dininghall: null             // 确认后的场所ID
})

// 计算属性
const hasActiveFilters = computed(() => {
  // 只有确认后的筛选条件才显示在页面上
  return confirmedFilters.startDate || confirmedFilters.endDate || confirmedFilters.dininghall
})

const activeFiltersText = computed(() => {
  const filters = []
  if (confirmedFilters.startDate && confirmedFilters.endDate) {
    filters.push(`${confirmedFilters.startDate} 至 ${confirmedFilters.endDate}`)
  } else if (confirmedFilters.startDate) {
    filters.push(`从 ${confirmedFilters.startDate}`)
  } else if (confirmedFilters.endDate) {
    filters.push(`至 ${confirmedFilters.endDate}`)
  }

  // 添加场所筛选显示
  if (confirmedFilters.dininghall) {
    const selectedDininghall = dininghallOptions.value.find(item => item.id === confirmedFilters.dininghall)
    if (selectedDininghall) {
      filters.push(`场所：${selectedDininghall.title}`)
    }
  }

  return filters.join('，')
})

// ECharts 饼图配置
const getPieChartOption = () => {
  return {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e5e5',
      borderWidth: 1,
      textStyle: {
        color: '#323233',
        fontSize: 12
      },
      formatter: function(params) {
        return `<div style="font-weight: 500; margin-bottom: 4px;">${params.name}</div>
                <div style="display: flex; align-items: center;">
                  <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background: ${params.color}; margin-right: 6px;"></span>
                  <span style="margin-right: 8px;">数量</span>
                  <span style="font-weight: 500;">${params.value}</span>
                  <span style="margin-left: 4px; color: #969799;">(${params.percent}%)</span>
                </div>`
      }
    },
    legend: {
      show: false // 我们使用自定义图例
    },
    series: [
      {
        name: '场所占比',
        type: 'pie',
        radius: ['0%', '55%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: function(params) {
            return `{name|${params.name}}\n{value|${params.value}}`
          },
          rich: {
            name: {
              fontSize: 12,
              fontWeight: 600,
              lineHeight: 16
            },
            value: {
              fontSize: 11,
              color: '#969799',
              lineHeight: 14
            }
          }
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          lineStyle: {
            color: '#ddd'
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        data: chartData.value.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  }
}

// 方法
const openFilterPopup = () => {
  // 打开筛选弹窗时，将确认后的筛选条件复制到选择状态
  selectedFilters.startDate = confirmedFilters.startDate
  selectedFilters.endDate = confirmedFilters.endDate
  selectedFilters.dininghall = confirmedFilters.dininghall

  // 重置导航状态
  activeNavItem.value = 'date'

  showFilterPopup.value = true
}

const scrollToSection = (sectionName) => {
  activeNavItem.value = sectionName

  // 获取对应的ref元素，日期导航默认滚动到开始时间
  let targetRef = null
  switch (sectionName) {
    case 'date':
      targetRef = startDateRef.value
      break
    case 'startDate':
      targetRef = startDateRef.value
      break
    case 'endDate':
      targetRef = endDateRef.value
      break
    case 'dininghall':
      targetRef = dininghallRef.value
      break
  }

  // 滚动到目标元素
  if (targetRef && filterContentRef.value) {
    const container = filterContentRef.value
    const target = targetRef
    const containerTop = container.scrollTop
    const targetTop = target.offsetTop - container.offsetTop

    container.scrollTo({
      top: targetTop,
      behavior: 'smooth'
    })
  }
}

const openDatePicker = (type) => {
  currentDateType.value = type
  // 设置当前日期为已选择的日期或今天
  const targetDate = type === 'start' ? selectedFilters.startDate : selectedFilters.endDate
  if (targetDate) {
    const dateParts = targetDate.split('-')
    currentDate.value = dateParts
  } else {
    const today = new Date()
    currentDate.value = [
      today.getFullYear().toString(),
      (today.getMonth() + 1).toString().padStart(2, '0'),
      today.getDate().toString().padStart(2, '0')
    ]
  }
  showDatePicker.value = true
}

const onDateConfirm = () => {
  const dateStr = currentDate.value.join('-')
  if (currentDateType.value === 'start') {
    selectedFilters.startDate = dateStr
  } else {
    selectedFilters.endDate = dateStr
  }
  showDatePicker.value = false
}

// 场所选择方法
const selectFilter = (type, value) => {
  if (type === 'dininghall') {
    selectedFilters.dininghall = selectedFilters.dininghall === value ? null : value
  }
}

const onFilterReset = () => {
  selectedFilters.startDate = ''
  selectedFilters.endDate = ''
  selectedFilters.dininghall = null
}

const onFilterConfirm = () => {
  // 确认时将选择的筛选条件复制到确认状态
  confirmedFilters.startDate = selectedFilters.startDate
  confirmedFilters.endDate = selectedFilters.endDate
  confirmedFilters.dininghall = selectedFilters.dininghall

  showFilterPopup.value = false
  // 重新加载场所数据
  loadVenueData()
}

// ECharts 管理方法
const initChart = () => {
  if (chartRef.value && !chartInstance) {
    chartInstance = echarts.init(chartRef.value)
    updateChart()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  }
}

const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(getPieChartOption(), true)
  }
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

const destroyChart = () => {
  if (chartInstance) {
    window.removeEventListener('resize', handleResize)
    chartInstance.dispose()
    chartInstance = null
  }
}

// 获取场所选项数据
const loadDininghallOptions = async () => {
  try {
    const response = await proxy.$get('/dininghall/get_all', { type: 0 })
    if (response.code === 200) {
      dininghallOptions.value = response.data.map(item => ({
        id: item.id,
        title: item.title
      }))
    } else {
      console.error('获取场所数据失败:', response.msg)
    }
  } catch (error) {
    console.error('获取场所数据失败:', error)
  }
}

// 加载场所数据
const loadVenueData = async () => {
  try {
    // 构建请求参数，过滤空值
    const params = {}
    if (confirmedFilters.startDate) params.start_date = confirmedFilters.startDate
    if (confirmedFilters.endDate) params.end_date = confirmedFilters.endDate
    if (confirmedFilters.dininghall) params.dininghall_id = confirmedFilters.dininghall

    const response = await proxy.$post('statistics/venue_data', params)

    if (!response.errcode) {
      chartData.value = response.result || chartData.value
      // 数据更新后重新渲染图表
      nextTick(() => {
        updateChart()
      })
    } else {
      showToast(response.errmsg || '获取场所数据失败')
    }
  } catch (error) {
    console.error('加载场所数据失败:', error)
    showToast('获取场所数据失败')
  }
}

// 生命周期
onMounted(async () => {
  await loadDininghallOptions()
  await loadVenueData()
  await nextTick()
  initChart()
})

onUnmounted(() => {
  destroyChart()
})
</script>

<style lang="scss" scoped>
.venue-stats {
  padding: 0 16px;
  padding-top: 16px;
}

.venue-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

// 头部区域样式
.header-section {
  margin-bottom: 16px;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.venue-title {
  font-size: 15px;
  font-weight: 500;
  color: #323233;
  line-height: 21px;
}

.filter-selector {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;

}

.filter-text {
  font-size: 14px;
  color: #1989FA;
  line-height: 20px;
}

.filter-tags {
  padding: 8px;
  background: #F7F8FA;
  border-radius: 8px;
  font-size: 12px;
  color: #969799;
  line-height: 16px;
}

.chart-wrapper {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 16px;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #646566;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.chart-container {
  margin: 0;
}

.echarts-chart {
  width: 100%;
  height: 280px;
}

// 筛选弹窗样式 - 与TrendStats保持一致
.filter-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f7f8fa;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #ebedf0;
}

.filter-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

/* {{ AURA-X: Add - 左右分栏布局 }} */
.filter-body {
  z-index: 1000;
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* {{ AURA-X: Add - 左侧导航样式 }} */
.filter-nav {
  width: 80px;
  background: #f2f3f5;
  overflow-y: auto;
}

/* {{ AURA-X: Modify - 重新设计导航项样式，支持红色指示点和完整白色背景 }} */
.filter-nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  line-height: 19px;
  cursor: pointer;
  font-size: 13px;
  color: #323233;
  &.active {
    background: #fff;

    .nav-text {
      color: #323233;
    }
  }

}

/* {{ AURA-X: Add - 导航指示器样式，支持红色数据指示 }} */
.nav-indicator {
  position: absolute;
  top: 26px;
  left: 19px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: transparent;
  transition: all 0.2s;

  &.has-data {
    background: #ff4444;
  }
}

.nav-text {
  font-size: 13px;
  color: #646566;
  text-align: center;
  line-height: 19px;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
  background: #fff;
}

/* {{ AURA-X: Add - 表单项样式，类似图片中的设计 }} */
.filter-form-item {
  background: #fff;
  overflow: hidden;
}

.filter-label {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #fff;
}

.label-text {
  font-size: 14px;
  line-height: 20px;
  color: #323233;
}

.filter-value {
  padding: 0 16px;
  background: #fff;
  .van-field{
    border: 0.5px solid #eaeaea;
    border-radius: 8px;
    margin-bottom: 16px;
  }
}

.filter-select-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-select-option {
  padding: 6px 12px;
  background: #F2F3F5;
  border-radius: 8px;
  font-size: 13px;
  line-height: 20px;
  color: #323233;
  cursor: pointer;
  min-width: 82px;
  text-align: center;

  &.active {
    background: rgba(25, 137, 250, 0.1);
    color: #1989FA;
  }
}

/* {{ AURA-X: Modify - 重新设计底部按钮样式 }} */
.filter-footer {
  display: flex;
  padding: 5px 16px;
  padding-bottom: 16px;
  gap: 12px;
  background: #fff;
}

.filter-button {
  flex: 1;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 22px;
  font-size: 16px;
}

.reset-button {
  color: #323233;
  border: 0.5px solid #DCDEE0;
}

.confirm-button {
  background: #1989fa;
  color: #fff;
}
</style>
