<template>
  <div class="wrapper-transfer">
    <!-- {{ AURA-X: Replace - 重构转账页面，实现分组边框和钉钉选人功能. Approval: 寸止(ID:pending). }} -->

    <!-- 转入员工选择区域 - 作为一个整体内容块 -->
    <van-cell-group inset>
      <!-- 转入员工单选 -->
      <van-field name="transfer_type" label="转入员工" input-align="right">
        <template #input>
          <van-radio-group v-model="transferType" direction="horizontal">
            <van-radio :name="0" shape="dot">内部员工</van-radio>
            <van-radio :name="1" shape="dot">外部人员</van-radio>
          </van-radio-group>
        </template>
      </van-field>

      <!-- 内部员工选择 -->
      <van-field
        v-if="transferType === 0"
        v-model="internalPersonText"
        name="internal_person"
        label="内部员工"
        placeholder="请选择内部员工"
        readonly
        is-link
        input-align="left"
        @click="selectInternalPerson"
      />

      <!-- 外部人员选择 -->
      <van-field
        v-if="transferType === 1"
        v-model="externalPersonText"
        name="external_person"
        label="外部人员"
        placeholder="请选择外部人员"
        readonly
        is-link
        input-align="left"
        @click="selectExternalPerson"
      />
    </van-cell-group>

    <!-- 充值金额 - 恢复之前的样式 -->
    <div class="money">
      <van-cell-group inset style="padding: 4px 0;margin-bottom: 10px;">
        <van-field
          v-model="transferAmount"
          type="number"
          name="amount"
          label="充值金额"
          placeholder="请输入金额"
          label-align="top"
          :formatter="formatAmount"
          @input="onAmountInput"
        />
      </van-cell-group>
      <span class="icon">¥</span>
    </div>

    <!-- 备注 -->
    <van-cell-group inset>
      <van-field
        v-model="remark"
        name="remark"
        label="备注"
        type="textarea"
        placeholder="请输入备注"
        maxlength="100"
        show-word-limit
        autosize
      />
    </van-cell-group>

    <!-- 转账按钮 -->
    <div style="margin: 16px" v-if="canSubmit">
      <van-button
        :loading="loading"
        block
        color="#1989FA"
        @click="onSubmit"
      >
        转账
      </van-button>
    </div>
    <div style="margin: 16px" v-else>
      <van-button
        :loading="loading"
        block
        disabled
        color="#dcdee0"
        @click="onSubmit"
      >
        转账
      </van-button>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
import { showToast, showDialog } from "vant";

// {{ AURA-X: Replace - 重构转账页面逻辑，添加内部员工选择和钉钉选人功能. Approval: 寸止(ID:pending). }}
// 转账相关数据
const transferType = ref(0); // 0: 内部员工, 1: 外部人员
const internalPersonText = ref(""); // 内部员工选择显示文本
const externalPersonText = ref(""); // 外部人员选择显示文本
const transferAmount = ref(""); // 转账金额
const remark = ref(""); // 备注
const loading = ref(false);

// 存储选中的人员信息
const selectedInternalPerson = ref(null);
const selectedExternalPerson = ref(null);

// 计算是否可以提交
const canSubmit = computed(() => {
  // 必须有转账金额和备注
  if (!transferAmount.value || !remark.value) {
    return false;
  }

  // 如果选择内部员工，必须选择具体人员
  if (transferType.value === 0 && !internalPersonText.value) {
    return false;
  }

  // 如果选择外部人员，必须选择具体人员
  if (transferType.value === 1 && !externalPersonText.value) {
    return false;
  }

  return true;
});

// 格式化金额输入（限制两位小数）
const formatAmount = (value) => {
  // 只允许数字和一个小数点，最多两位小数
  const regex = /^\d*\.?\d{0,2}$/;
  if (!regex.test(value)) {
    const validPart = value.match(/^\d*\.?\d{0,2}/)?.[0] || '';
    return validPart;
  }
  return value;
};

// 金额输入处理
const onAmountInput = (value) => {
  transferAmount.value = formatAmount(value);
};

// 选择内部员工 - 调用钉钉选人
const selectInternalPerson = () => {
  if (!proxy.$_dd) {
    showToast("钉钉环境未初始化");
    return;
  }

  proxy.$_dd.biz.contact.complexChoose({
    title: "选择内部员工",
    multiple: false, // 单选
    limitTips: "只能选择一个员工",
    maxUsers: 1,
    pickedUsers: selectedInternalPerson.value ? [selectedInternalPerson.value.userId] : [],
    pickedDepartments: [],
    disabledUsers: [],
    disabledDepartments: [],
    requiredUsers: [],
    requiredDepartments: [],
    appId: proxy.$_dd.env.corpId,
    permissionType: "GLOBAL",
    responseUserOnly: true,
    startWithDepartmentId: 0,
    onSuccess: function (result) {
      console.log("选择内部员工成功:", result);
      if (result.users && result.users.length > 0) {
        const user = result.users[0];
        selectedInternalPerson.value = user;
        internalPersonText.value = user.name;
      }
    },
    onFail: function (err) {
      console.error("选择内部员工失败:", err);
      showToast("选择员工失败");
    }
  });
};

// 选择外部人员
const selectExternalPerson = () => {
  // TODO: 实现外部人员选择逻辑
  showToast("外部人员选择功能待实现");
  // 临时设置示例文本
  externalPersonText.value = "已选择个人";
};

// 提交转账
const onSubmit = () => {
  if (!canSubmit.value) {
    showToast("请完善转账信息");
    return;
  }

  const transferData = {
    transfer_type: transferType.value,
    amount: transferAmount.value,
    remark: remark.value,
  };

  // 如果是内部员工，添加员工信息
  if (transferType.value === 0 && selectedInternalPerson.value) {
    transferData.internal_person = {
      userId: selectedInternalPerson.value.userId,
      name: selectedInternalPerson.value.name,
      avatar: selectedInternalPerson.value.avatar || ""
    };
  }

  // 如果是外部人员，添加人员信息
  if (transferType.value === 1) {
    transferData.external_person = externalPersonText.value;
  }

  showDialog({
    title: "转账确认",
    message: `转账金额：¥${transferAmount.value}元\n备注：${remark.value}\n确认转账吗？`,
  })
    .then(() => {
      submitTransfer(transferData);
    })
    .catch(() => {
      // 用户取消
    });
};

// 提交转账请求
const submitTransfer = (data) => {
  loading.value = true;

  // TODO: 替换为实际的转账API
  proxy
    .$post(import.meta.env.VITE_APP_USER_API + "transfer/post_add", data)
    .then((res) => {
      if (!res.errcode) {
        showToast("转账成功");
        // 重置表单
        resetForm();
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      showToast(err || "转账失败");
    })
    .finally(() => {
      loading.value = false;
    });
};

// 重置表单
const resetForm = () => {
  transferType.value = 0;
  internalPersonText.value = "";
  externalPersonText.value = "";
  transferAmount.value = "";
  remark.value = "";
  selectedInternalPerson.value = null;
  selectedExternalPerson.value = null;
};
</script>
<style lang="scss">
// {{ AURA-X: Replace - 更新转账页面样式，恢复充值金额样式. Approval: 寸止(ID:pending). }}
.wrapper-transfer {
  width: 100%;
  min-height: 100vh;
  padding-top: 16px;
  position: relative;

  // 恢复之前的充值金额样式
  .money {
    position: relative;
    .van-field__label {
      color: #171A1D;
      font-size: 16px;
      margin-bottom: 16px;
    }

    .van-field__control {
      font-size: 32px;
      margin-left: 22px;
      line-height: 38px;
      .van-field__control-input-placeholder {
        //  font-weight: 500;
      }
    }
  }

  .van-field__label {
    color: #171A1D;
    font-size: 16px;
  }

  .van-field__control{
    font-size: 16px;
    line-height: 22px;
  }

  .icon {
    position: absolute;
    left: 33px;
    top: 68px;
    font-size: 20px;
    color: #171A1D;
    line-height: 24px;
  }

  .van-icon {
    margin-right: 8px;
  }

  .van-cell{
    color: #171A1D;
    font-size: 16px;
    padding: 12px 16px;
    line-height: 22px;
  }

  // 单选框样式
  .van-radio-group {
    .van-radio {
      margin-right: 24px;

      &:last-child {
        margin-right: 0;
      }
    }

    .van-radio__label {
      color: #323233;
      font-size: 16px;
    }

    .van-radio__icon--checked {
      .van-icon {
        background-color: #1989fa;
        border-color: #1989fa;
      }
    }
  }

  // 按钮样式
  .van-button{
    border-radius: 22px;
  }

  // 表单组间距
  .van-cell-group {
    margin-bottom: 12px;

    &:last-of-type {
      margin-bottom: 0;
    }
  }

  // 文本域样式
  .van-field--textarea {
    .van-field__control {
      min-height: 60px;
    }
  }

  // 链接字段样式
  .van-field--clickable {
    .van-field__control {
      color: #969799;
    }

    &.van-field--readonly {
      .van-field__control {
        color: #323233;
      }
    }
  }
}
</style>
