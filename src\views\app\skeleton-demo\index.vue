<template>
  <div class="skeleton-demo">
    <yhc-header title="骨架屏演示" />
    
    <div class="demo-section">
      <h3>基础骨架屏</h3>
      <van-skeleton :loading="loading1" :row="3" />
      <van-button @click="toggleLoading1" type="primary" size="small">
        {{ loading1 ? '停止加载' : '开始加载' }}
      </van-button>
    </div>

    <div class="demo-section">
      <h3>带头像的骨架屏</h3>
      <van-skeleton :loading="loading2" avatar :row="3" />
      <van-button @click="toggleLoading2" type="primary" size="small">
        {{ loading2 ? '停止加载' : '开始加载' }}
      </van-button>
    </div>

    <div class="demo-section">
      <h3>自定义骨架屏</h3>
      <van-skeleton :loading="loading3" :row="4" :row-width="['100%', '60%', '80%', '100%']" />
      <van-button @click="toggleLoading3" type="primary" size="small">
        {{ loading3 ? '停止加载' : '开始加载' }}
      </van-button>
    </div>

    <div class="demo-section">
      <h3>列表骨架屏</h3>
      <div v-if="loading4">
        <van-skeleton 
          v-for="i in 3" 
          :key="i" 
          avatar 
          :row="2" 
          :row-width="['100%', '60%']"
          class="skeleton-item"
        />
      </div>
      <div v-else>
        <div class="list-item" v-for="i in 3" :key="i">
          <div class="avatar"></div>
          <div class="content">
            <div class="title">列表项标题 {{ i }}</div>
            <div class="desc">这是列表项的描述内容</div>
          </div>
        </div>
      </div>
      <van-button @click="toggleLoading4" type="primary" size="small">
        {{ loading4 ? '停止加载' : '开始加载' }}
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const loading1 = ref(true);
const loading2 = ref(true);
const loading3 = ref(true);
const loading4 = ref(true);

const toggleLoading1 = () => {
  loading1.value = !loading1.value;
};

const toggleLoading2 = () => {
  loading2.value = !loading2.value;
};

const toggleLoading3 = () => {
  loading3.value = !loading3.value;
};

const toggleLoading4 = () => {
  loading4.value = !loading4.value;
};
</script>

<style lang="scss" scoped>
.skeleton-demo {
  padding: 16px;
  background: #f7f8fa;
  min-height: 100vh;
}

.demo-section {
  background: white;
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 8px;

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #323233;
  }

  .van-button {
    margin-top: 16px;
  }
}

.skeleton-item {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.list-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ebedf0;
    margin-right: 12px;
  }

  .content {
    flex: 1;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #323233;
      margin-bottom: 4px;
    }

    .desc {
      font-size: 14px;
      color: #969799;
    }
  }
}
</style>
