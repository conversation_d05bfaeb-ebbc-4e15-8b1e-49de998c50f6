<template>
  <div class="select-user-container">
    <!-- 搜索框 -->
    <van-search
      v-model="searchValue"
      placeholder="搜索人员姓名、部门或电话"
      class="search-bar"
    />
    
    <!-- 人员列表 -->
    <div class="user-list">
      <van-checkbox-group v-model="selectedUsers">
        <template v-for="user in filteredUsers" :key="user.id">
          <div class="user-item">
            <van-checkbox :name="user.id" class="user-checkbox">
              <div class="user-info">
                <img :src="user.avatar" alt="" class="user-avatar" />
                <div class="user-details">
                  <div class="user-name">{{ user.name }}</div>
                  <div class="user-dept">{{ user.department || '暂无部门' }}</div>
                  <div class="user-phone">{{ user.phone || '暂无电话' }}</div>
                </div>
              </div>
            </van-checkbox>
          </div>
        </template>
      </van-checkbox-group>
      
      <!-- 空状态 -->
      <van-empty 
        v-if="filteredUsers.length === 0" 
        description="暂无人员数据"
        image="search"
      />
    </div>
    
    <!-- 底部操作栏 -->
    <van-action-bar class="action-bar">
      <van-action-bar-icon 
        icon="clear" 
        text="清空" 
        @click="clearSelection"
        v-if="selectedUsers.length > 0"
      />
      <van-action-bar-button 
        type="primary" 
        :text="`确定(${selectedUsers.length})`"
        @click="confirmSelection"
      />
    </van-action-bar>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { useLoginStore } from '@/store/dingLogin'

const router = useRouter()
const app = useLoginStore()

// 搜索值
const searchValue = ref('')

// 选中的人员
const selectedUsers = ref([])

// 模拟人员数据
const userList = ref([
  {
    id: '1',
    name: '张三',
    department: '技术部',
    phone: '13800138001',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    id: '2', 
    name: '李四',
    department: '产品部',
    phone: '13800138002',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    id: '3',
    name: '王五',
    department: '设计部', 
    phone: '13800138003',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    id: '4',
    name: '赵六',
    department: '运营部',
    phone: '13800138004', 
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    id: '5',
    name: '孙七',
    department: '市场部',
    phone: '13800138005',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    id: '6',
    name: '周八',
    department: '财务部',
    phone: '13800138006',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    id: '7',
    name: '吴九',
    department: '人事部',
    phone: '13800138007',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    id: '8',
    name: '郑十',
    department: '行政部',
    phone: '13800138008',
    avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
  }
])

// 过滤后的人员列表
const filteredUsers = computed(() => {
  if (!searchValue.value) {
    return userList.value
  }
  return userList.value.filter(user => 
    user.name.toLowerCase().includes(searchValue.value.toLowerCase()) ||
    (user.department && user.department.toLowerCase().includes(searchValue.value.toLowerCase())) ||
    (user.phone && user.phone.includes(searchValue.value))
  )
})

// 页面加载
onMounted(() => {
  // 如果有之前选择的人员，恢复选择状态（使用清空待还的状态）
  if (app.clearDebt.externalUserList && app.clearDebt.externalUserList.length > 0) {
    selectedUsers.value = app.clearDebt.externalUserList.map(item => item.id)
  }
  
  // 加载人员列表
  loadUserList()
})

// 加载人员列表
const loadUserList = async () => {
  try {
    // 这里可以调用真实的API
    // const response = await proxy.$get('external-user/list')
    // userList.value = response.data
    
    // 目前使用模拟数据
    console.log('外部人员列表加载完成')
  } catch (error) {
    console.error('加载人员列表失败:', error)
    showToast('加载人员列表失败')
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 清空选择
const clearSelection = () => {
  selectedUsers.value = []
}

// 确认选择
const confirmSelection = () => {
  if (selectedUsers.value.length === 0) {
    showToast({
      message: '请选择人员',
      type: 'text',
      duration: 2000
    })
    return
  }
  
  // 构造选中的人员数据
  const selectedUserData = userList.value.filter(user => 
    selectedUsers.value.includes(user.id)
  ).map(user => ({
    id: user.id,
    name: user.name,
    department: user.department,
    phone: user.phone,
    avatar: user.avatar
  }))
  
  // 保存到store（使用清空待还的状态）
  app.clearDebt.externalUserList = selectedUserData
  
  showToast(`已选择${selectedUsers.value.length}个人员`)
  
  // 返回上一页
  setTimeout(() => {
    router.go(-1)
  }, 1000)
}
</script>

<style lang="scss" scoped>
.select-user-container {
  background: #f2f3f4;
  min-height: 100vh;
  padding-bottom: 60px;
}

.search-bar {
  background: #fff;
  padding: 8px 16px;
  
  :deep(.van-search__content) {
    background: #f7f8fa;
    border-radius: 20px;
  }
}

.user-list {
  padding: 8px 0;
}

.user-item {
  background: #fff;
  margin: 8px 16px;
  border-radius: 8px;
  overflow: hidden;
  
  .user-checkbox {
    width: 100%;
    padding: 12px 16px;
    
    :deep(.van-checkbox__label) {
      width: 100%;
      margin-left: 8px;
    }
  }
  
  .user-info {
    display: flex;
    align-items: center;
    width: 100%;
  }
  
  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
  }
  
  .user-details {
    flex: 1;
    min-width: 0;
  }
  
  .user-name {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 4px;
  }
  
  .user-dept {
    font-size: 14px;
    color: #969799;
    margin-bottom: 2px;
  }
  
  .user-phone {
    font-size: 12px;
    color: #c8c9cc;
  }
}

.action-bar {
  :deep(.van-action-bar-button) {
    background: #1989fa;
  }
}
</style>
