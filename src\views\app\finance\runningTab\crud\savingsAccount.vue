<template>
  <div class="savings-account-container">
    <!-- 搜索栏 -->
    <!-- <div class="search-header">
      <van-search v-model="value" placeholder="搜索" />
      <div class="filter-btn" @click="showFilter = true">
        筛选
      </div>
    </div> -->
    <yhc-list :key="listConfig.key || 'default'" :config="listConfig" :filterForm="filterForm" @onButClick="onAddClick" ref="listRef">
      <template #default="{ item, index }">
        <div  class="transaction-item"
            @click="showTransactionDetail(item)">
            <div class="item-left">
              <div class="avatar">
                <van-image :src="item.avatar" width="44" height="44" round />
              </div>
              <div class="item-info">
                <div class="name">{{ item.operator_name }}</div>
                <div class="type" style="padding: 7px 0;">{{ item.dept_name }}</div>
                <div class="time">{{ item.transaction_time }}</div>
              </div>
            </div>
            <div class="item-right">
              <div class="amount" :class="{ 'negative': item.price < 0, 'positive': item.price > 0 }">
                {{ item.price > 0 ? '+' : '' }}{{ item.price }}
              </div>
              <div  v-for="(item1,index) in changeTypeTags" :key="index" >
                <!-- {{ item.transaction_type }}
                {{ item1.type }} -->
                <div class="status" :class="item1.value" v-if="item.transaction_type==item1.type">
                  {{ item1.label }}
                </div>
              </div>
            </div>
          </div>
        </template>
    </yhc-list>
    <!-- 交易记录列表 -->
    <!-- <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
        <div class="transaction-list">
          <div v-for="(item, index) in transactionList" :key="index" class="transaction-item"
            @click="showTransactionDetail(item)">
            <div class="item-left">
              <div class="avatar">
                <van-image :src="item.avatar" width="44" height="44" round />
              </div>
              <div class="item-info">
                <div class="name">{{ item.name }}</div>
                <div class="type" style="padding: 7px 0;">{{ item.type }}</div>
                <div class="time">{{ item.time }}</div>
              </div>
            </div>
            <div class="item-right">
              <div class="amount" :class="{ 'negative': item.amount < 0, 'positive': item.amount > 0 }">
                {{ item.amount > 0 ? '+' : '' }}{{ item.amount }}
              </div>
              <div class="status" :class="item.statusClass">
                {{ item.status }}
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh> -->
    <!-- 筛选弹窗（底部弹出） -->
    <van-popup v-model:show="showFilter" position="bottom"
      :style="{ borderTopLeftRadius: '16px', borderTopRightRadius: '16px', minHeight: '60vh', maxHeight: '90vh' }" round
      closeable close-icon="close" safe-area-inset-bottom>
      <div class="filter-popup-content">
        <div class="filter-popup-header">
          <span>全部筛选</span>
        </div>
        <div class="filter-popup-body">
          <!-- 日期 -->
          <div class="filter-row">
            <div class="filter-label-left">日期</div>
            <div class="filter-content-right">
              <div class="date-range-fields">
                <div>日期</div>
                <div
                  style="font-size: 16px; color: #C8C9CC;width: 263px;border: 1px solid #C8C9CC;height: 36px;display: flex; align-items: center;padding-left: 5px;border-radius: 10px;margin-top: 16px;"
                  @click="showStartCalendar = true">
                  <van-icon name="underway-o" /><span style="color: #000;margin-left: 9px;"
                    v-if="filterForm.start_time">{{
                      filterForm.start_time }}</span> <span v-else style="margin-left: 9px;">开始日期</span>
                </div>
                <div
                  style="font-size: 16px; color: #C8C9CC;width: 263px;border: 1px solid #C8C9CC;height: 36px;display: flex; align-items: center;padding-left: 5px;border-radius: 10px;margin-top: 16px;"
                  @click="showEndCalendar = true">
                  <van-icon name="underway-o" /><span style="color: #000;margin-left: 9px;" v-if="filterForm.end_time">{{
                    filterForm.end_time }}</span> <span v-else style="margin-left: 9px;">结束日期</span>
                </div>
                <!-- <van-field v-model="filterForm.start_time" placeholder="开始日期" readonly @click="showStartCalendar = true"
                  class="date-field" /> -->
                <!-- <span class="date-sep">-</span>
                <van-field v-model="filterForm.end_time" placeholder="结束日期" readonly @click="showEndCalendar = true"
                  class="date-field" /> -->
              </div>
            </div>
          </div>

          <!-- 金额 -->
          <div class="filter-row" style="margin-top: -20px;">
            <div class="filter-label-left">金额</div>
            <div class="filter-content-right">
              <div>金额</div>
              <div class="amount-range-fields" style="margin-top: 16px;">
                <div style="display: flex; align-items: center; border: 1px solid #e6e6e6; color: #c8c9cc;
        border-radius: 6px;">
                  <span style="margin-left: 8px;">￥</span>
                  <van-field v-model="filterForm.min_amount" placeholder="最小金额" type="number" class="amount-field" />
                </div>
                <span class="amount-sep">-</span>
                <div style="display: flex; align-items: center; border: 1px solid #e6e6e6; color: #c8c9cc;
        border-radius: 6px;">
                  <span style="margin-left: 8px;">￥</span>
                  <van-field v-model="filterForm.max_amount" placeholder="最大金额" type="number" class="amount-field" />
                </div>
              </div>
            </div>
          </div>

          <!-- 收支 -->
          <div class="filter-row" style="margin-top: -22px;">
            <div class="filter-label-left">收支</div>
            <div class="filter-content-right">
              <div>收支</div>
              <div class="income-type-options">
                <van-button size="large" :type="filterForm.type === null ? 'primary' : 'default'"
                  @click="filterForm.type = null" class="income-type-btn">全部</van-button>
                <van-button size="large" :type="filterForm.type === 0 ? 'primary' : 'default'"
                  @click="filterForm.type = 0" class="income-type-btn">收入</van-button>
                <van-button size="large" :type="filterForm.type === 1 ? 'primary' : 'default'"
                  @click="filterForm.type = 1" class="income-type-btn">支出</van-button>
              </div>
            </div>
          </div>

          <!-- 变动类型 -->
          <div class="filter-row">
            <div class="filter-label-left">变动类型</div>
            <div class="filter-content-right">
              <div class="change-type-grid">
                <van-button v-for="tag in visibleChangeTypeTags" :key="tag.value" size="small"
                  :type="filterForm.changeType === tag.value ? 'primary' : 'default'"
                  @click="selectChangeType(tag.value)" class="change-type-btn">{{ tag.label }}</van-button>
                <van-button size="small" type="default" @click="showMoreChangeType = !showMoreChangeType"
                  class="more-btn" style="border: none;color: #969799;" v-if="!showMoreChangeType"> 更多<van-icon
                    name="arrow-down" /></van-button>
                <van-button size="small" type="default" @click="showMoreChangeType = !showMoreChangeType"
                  class="more-btn" style="border: none;color: #969799;" v-else> 收起<van-icon
                    name="arrow-up" /></van-button>
              </div>
              <div class="more-options" v-if="changeTypeTags.length > 6">
              </div>
            </div>
          </div>
        </div>
        <div class="filter-popup-actions">
          <van-button type="default" block @click="resetFilter">重置</van-button>
          <van-button type="primary" block @click="confirmFilter">确定</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 开始时间选择器 -->
    <van-popup v-model:show="showStartCalendar" position="bottom">
      <van-picker :columns="dateColumns" title="选择开始时间" @confirm="confirmStartDate"
        @cancel="showStartCalendar = false" />
    </van-popup>

    <!-- 结束时间选择器 -->
    <van-popup v-model:show="showEndCalendar" position="bottom">
      <van-picker :columns="dateColumns" title="选择结束时间" @confirm="confirmEndDate" @cancel="showEndCalendar = false" />
    </van-popup>

    <!-- 交易详情弹窗 -->
    <van-popup v-model:show="showDetail" position="bottom" :style="{ height: '70%' }" round>
      <div class="detail-content">
        <div class="detail-header">
          <span>交易详情</span>
          <van-icon name="cross" @click="showDetail = false" />
        </div>

        <div class="detail-body" v-if="currentTransaction">
          <!-- 基本信息 -->
          <div class="detail-section">
            <div class="section-title">基本信息</div>
            <div class="detail-item">
              <span class="label">姓名：</span>
              <span class="value">{{ currentTransaction.name }}</span>
            </div>
            <div class="detail-item">
              <span class="label">部门：</span>
              <span class="value">{{ currentTransaction.type }}</span>
            </div>
            <div class="detail-item">
              <span class="label">金额：</span>
              <span class="value amount"
                :class="{ 'negative': currentTransaction.amount < 0, 'positive': currentTransaction.amount > 0 }">
                {{ currentTransaction.amount > 0 ? '+' : '' }}{{ currentTransaction.amount }}
              </span>
            </div>
          </div>

          <!-- 交易类型详情 -->
          <div class="detail-section">
            <div class="section-title">交易类型</div>
            <div class="detail-item">
              <span class="label">交易类型：</span>
              <span class="value transaction-type">{{ currentTransaction.status }}</span>
            </div>
          </div>

          <!-- 时间信息 -->
          <div class="detail-section">
            <div class="section-title">时间信息</div>
            <div class="detail-item">
              <span class="label">时间：</span>
              <span class="value">{{ currentTransaction.time }}</span>
            </div>
          </div>

          <!-- 备注信息 -->
          <div class="detail-section">
            <div class="section-title">备注信息</div>
            <div class="detail-item">
              <span class="label">订单号：</span>
              <span class="value">{{ generateOrderNumber(currentTransaction.id) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">业务订单号：</span>
              <span class="value">{{ generateBusinessOrderNumber(currentTransaction.id) }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, watch, computed, getCurrentInstance } from 'vue'
import { useRouter,useRoute } from 'vue-router'
// 筛选表单数据
const filterForm = ref({
  account_type:null,
  start_time: '',
  end_time: '',
  min_amount: '',
  max_amount: '',
  type:null,
  changeType: ''
})
const router = useRouter()
const route = useRoute()
filterForm.value.account_type=route.query.type
// 骨架屏配置
const skeletonConfig = reactive({
    isShow: true,
    count: 3,
    row: 2,
    rowWidth: ['100%', '60%', '80%'],
    avatar: false,
    avatarSize: '40px',
    avatarShape: 'round',
    title: true,
    titleWidth: '50%',
    duration: 500
})

// 列表组件引用
const listRef = ref(null)
// 列表配置
const listConfig = reactive({
    curl: {
        ls: '/transaction_flow/get_ls', // 留空，使用模拟数据
        // sort:'/dishes/post_sort'
    },
    // details:'/dishConfigDetail', // 当sort为true时，需要填写
    sort:false,// 是否排序 默认为不填
    title:'',
    postData: {
      ...filterForm.value
    },
    search: {
        isShow: true,
        isShowPopup: true,
        filtrateBottom:true
    },
    tabs: {
        isShow: false
    },
    button: {
        isShow: false,
    },
    skeleton: skeletonConfig,
    // 模拟数据格式化
    format: (data) => {
        // 这里可以对数据进行格式化处理
        console.log('格式化数据:', data)
    },
    // 添加模拟数据标识
    mockData: true
})

const { proxy } = getCurrentInstance();


const loading = ref(false)
const finished = ref(false);
const refreshing = ref(false);
// 搜索值
const searchValue = ref('')

// 筛选弹窗显示状态
const showFilter = ref(false)

// 详情弹窗显示状态
const showDetail = ref(false)

// 当前选中的交易记录
const currentTransaction = ref(null)
function getList() {
  console.log(filterForm.value,777)
  console.log(typeof filterForm.value.account_type,888)
  proxy.$get('/transaction_flow/get_ls',filterForm.value).then((res) => {
    if (res.code == 200) {
      transactionList.value = res.data.items
      console.log(transactionList.value.length)
      console.log(res.data.total)
      loading.value = false
      if (transactionList.value.length == res.data.total) {
        finished.value = true
      }
    }
  }).catch((err) => {
    loading.value = false
    finished.value = true
    console.log(err)
  })
}
//上拉加载
const onLoad = () => {
  filterForm.value.perPage+=10
  getList()
  console.log('onLoad')
}
//下拉刷新
const onRefresh = () => {
  finished.value = false;
  loading.value = true;
  getList()
  console.log('onRefresh')
}


// 添加监听器来调试
watch(filterForm, (newVal) => {
  console.log('filterForm 变化:', newVal)
}, { deep: true })

// 日期选择器显示状态
const showStartCalendar = ref(false)
const showEndCalendar = ref(false)

// 生成日期选择器的列数据
const generateDateColumns = () => {
  const currentYear = new Date().getFullYear()
  const years = []
  const months = []
  const days = []

  // 生成年份（当前年份前后5年）
  for (let i = currentYear - 5; i <= currentYear + 5; i++) {
    years.push({ text: `${i}年`, value: i })
  }

  // 生成月份
  for (let i = 1; i <= 12; i++) {
    months.push({ text: `${i}月`, value: i })
  }

  // 生成日期
  for (let i = 1; i <= 31; i++) {
    days.push({ text: `${i}日`, value: i })
  }

  return [years, months, days]
}

const dateColumns = generateDateColumns()

// 确认开始日期
const confirmStartDate = ({ selectedValues }) => {
  console.log('开始日期选择值:', selectedValues)
  const formattedDate = formatPickerDate(selectedValues)
  console.log('格式化后的开始日期:', formattedDate)

  // 强制更新响应式数据
  filterForm.value = {
    ...filterForm.value,
    start_time: formattedDate
  }

  console.log('更新后的 filterForm:', filterForm.value)
  showStartCalendar.value = false
}

// 确认结束日期
const confirmEndDate = ({ selectedValues }) => {
  console.log('结束日期选择值:', selectedValues)
  const formattedDate = formatPickerDate(selectedValues)
  console.log('格式化后的结束日期:', formattedDate)

  // 强制更新响应式数据
  filterForm.value = {
    ...filterForm.value,
    end_time: formattedDate
  }

  console.log('更新后的 filterForm:', filterForm.value)
  showEndCalendar.value = false
}

// 格式化选择器日期为字符串
const formatPickerDate = (selectedValues) => {
  console.log('格式化选择器日期:', selectedValues)
  if (Array.isArray(selectedValues) && selectedValues.length >= 3) {
    const [year, month, day] = selectedValues
    const result = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
    console.log('格式化结果:', result)
    return result
  }
  return ''
}



// 重置筛选条件
const resetFilter = () => {
  filterForm.value.start_time=''
  filterForm.value.end_time=''
  filterForm.value.min_amount=''
  filterForm.value.max_amount=''
  filterForm.value.type=null
  filterForm.value.changeType=''
}

// 确认筛选
const confirmFilter = () => {
  console.log('筛选条件:', filterForm.value)
  // 这里可以添加具体的筛选逻辑
  showFilter.value = false
  getList()
}

// 显示交易详情
const showTransactionDetail = (transaction) => {
  // currentTransaction.value = transaction
  router.push({
    path: '/finance/runningTab/result',
    query: { id: transaction.id }
  })
  // showDetail.value = true
}



// 生成订单号
const generateOrderNumber = (id) => {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
  return `ORD${date}${String(id).padStart(6, '0')}`
}

// 生成业务订单号
const generateBusinessOrderNumber = (id) => {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
  return `BIZ${date}${String(id).padStart(6, '0')}`
}

// 模拟交易记录数据
const transactionList = ref(
  //   [
  //   {
  //     id: 1,
  //     name: '徐星辰',
  //     type: '软件事业部',
  //     time: '2025-07-23 10:01:20',
  //     amount: +50,
  //     status: '补贴充值',
  //     statusClass: 'subsidy-recharge',
  //     avatar: '/images/avatar1.png'
  //   },
  //   {
  //     id: 2,
  //     name: '李明',
  //     type: '技术部',
  //     time: '2025-07-23 09:45:15',
  //     amount: +100,
  //     status: '补贴到账',
  //     statusClass: 'subsidy-arrive',
  //     avatar: '/images/avatar2.png'
  //   },
  //   {
  //     id: 3,
  //     name: '王小红',
  //     type: '财务部',
  //     time: '2025-07-23 09:30:10',
  //     amount: -25.5,
  //     status: '支付宝储值',
  //     statusClass: 'alipay-save',
  //     avatar: '/images/avatar3.png'
  //   },
  //   {
  //     id: 4,
  //     name: '张三',
  //     type: '人事部',
  //     time: '2025-07-23 09:15:30',
  //     amount: -18.8,
  //     status: '支付宝退款',
  //     statusClass: 'alipay-refund',
  //     avatar: '/images/avatar4.png'
  //   },
  //   {
  //     id: 5,
  //     name: '刘芳',
  //     type: '市场部',
  //     time: '2025-07-23 09:00:45',
  //     amount: +200,
  //     status: '微信储值',
  //     statusClass: 'wechat-save',
  //     avatar: '/images/avatar5.png'
  //   },
  //   {
  //     id: 6,
  //     name: '陈伟',
  //     type: '开发部',
  //     time: '2025-07-23 08:45:20',
  //     amount: +80,
  //     status: '微信退款',
  //     statusClass: 'wechat-refund',
  //     avatar: '/images/avatar6.png'
  //   },
  //   {
  //     id: 7,
  //     name: '赵敏',
  //     type: '设计部',
  //     time: '2025-07-23 08:30:15',
  //     amount: -45.2,
  //     status: '消费支出',
  //     statusClass: 'consume-out',
  //     avatar: '/images/avatar7.png'
  //   },
  //   {
  //     id: 8,
  //     name: '孙丽',
  //     type: '运营部',
  //     time: '2025-07-23 08:15:10',
  //     amount: +30.0,
  //     status: '退款收入',
  //     statusClass: 'refund-income',
  //     avatar: '/images/avatar8.png'
  //   },
  //   {
  //     id: 9,
  //     name: '周杰',
  //     type: '测试部',
  //     time: '2025-07-23 08:00:05',
  //     amount: -15.0,
  //     status: '转账扣款',
  //     statusClass: 'transfer-deduct',
  //     avatar: '/images/avatar9.png'
  //   },
  //   {
  //     id: 10,
  //     name: '吴琳',
  //     type: '客服部',
  //     time: '2025-07-23 07:45:30',
  //     amount: +60.0,
  //     status: '转账收款',
  //     statusClass: 'transfer-receive',
  //     avatar: '/images/avatar10.png'
  //   }
  // ]
)




// 变动类型标签
const changeTypeTags = [
  { label: '补贴发放', value: 'subsidy-recharge',type:1 },
  { label: '补贴扣除', value: 'subsidy-arrive',type:2  },
  { label: '支付宝充值', value: 'alipay-save',type:3  },
  { label: '支付宝提现', value: 'alipay-refund' ,type:4 },
  { label: '微信充值', value: 'wechat-save',type:5  },
  { label: '微信提现', value: 'wechat-refund' ,type:6 },
  { label: '转账收款', value: 'transfer-receive',type:10  },
  { label: '转账扣款', value: 'transfer-deduct',type:9  },
  { label: '消费支出', value: 'consume-out',type:7  },
  { label: '退款收入', value: 'refund-income',type:8  }
]

const showMoreChangeType = ref(false)

// 计算可见的变动类型标签
const visibleChangeTypeTags = computed(() => {
  if (showMoreChangeType.value) {
    return changeTypeTags
  }
  return changeTypeTags.slice(0, 5)
})



// 选择变动类型
const selectChangeType = (type) => {
  filterForm.value.changeType = filterForm.value.changeType === type ? '' : type
}



// 格式化日期
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
</script>

<style lang="scss" scoped>
.savings-account-container {
  background: #f2f3f4;
  padding-bottom: 20px;
  overflow-x: hidden;
}

.search-header {
  display: flex;
  align-items: center;
  padding: 0px 12px;
  background: #fff;
  gap: 8px;

  .van-search {
    flex: 1;

    :deep(.van-search__content) {
      background: #f5f5f5 !important;
      border-radius: 20px !important;
      border: none !important;
      // padding: 8px 16px !important;
    }

    :deep(.van-field__control) {
      background: transparent !important;
      font-size: 14px !important;
      color: #999 !important;
    }

    :deep(.van-search__field) {
      background: transparent !important;
      padding: 0 !important;
    }

    :deep(.van-field__body) {
      background: transparent !important;
    }

    :deep(.van-icon-search) {
      color: #999 !important;
      font-size: 16px !important;
    }
  }

  .filter-btn {
    color: #323233;
    font-size: 16px;
    cursor: pointer;
  }
}

.transaction-list {
  margin-top: 10px;
  padding: 0 16px;
  width: 100%;
  box-sizing: border-box;
}

.transaction-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  height: 100px;
  box-sizing: border-box;

  &:hover {
    background: #f7f8fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  &:active {
    background: #ebedf0;
    transform: translateY(0);
  }

  .item-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .avatar {
      flex-shrink: 0;
      margin-top: -28px;
    }

    .item-info {
      .name {
        font-size: 16px;
        font-weight: 500;
        color: #323233;
        margin-bottom: 4px;
      }

      .type {
        font-size: 14px;
        color: #969799;
        margin-bottom: 4px;
      }

      .time {
        font-size: 12px;
        color: #c8c9cc;
      }
    }
  }

  .item-right {
    text-align: right;

    .amount {
      font-size: 18px;
      font-weight: 500;
      color: #323233;
      margin-bottom: 4px;

      &.negative {
        color: #ff4d4f;
      }

      &.positive {
        color: #52c41a;
      }
    }

    .status {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 4px;

      // 补贴充值（绿色）
      &.subsidy-recharge {
        background: #e8f5e8;
        color: #07c160;
      }

      // 补贴到账（绿色）
      &.subsidy-arrive {
        background: #e8f5e8;
        color: #07c160;
      }

      // 支付宝储值（蓝色）
      &.alipay-save {
        background: #e6f7ff;
        color: #1890ff;
      }

      // 支付宝退款（橙色）
      &.alipay-refund {
        background: #fff2e8;
        color: #fa8c16;
      }

      // 微信储值（绿色）
      &.wechat-save {
        background: #f0f9ff;
        color: #52c41a;
      }

      // 微信退款（绿色）
      &.wechat-refund {
        background: #f0f9ff;
        color: #52c41a;
      }

      // 消费支出（红色）
      &.consume-out {
        background: #fff1f0;
        color: #ff4d4f;
      }

      // 退款收入（绿色）
      &.refund-income {
        background: #f6ffed;
        color: #52c41a;
      }

      // 转账扣款（红色）
      &.transfer-deduct {
        background: #fff1f0;
        color: #ff4d4f;
      }

      // 转账收款（绿色）
      &.transfer-receive {
        background: #f6ffed;
        color: #52c41a;
      }
    }
  }
}

.filter-content {
  padding: 16px;
  height: 100%;
  background: #fff;

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebedf0;
    margin-bottom: 16px;

    span {
      font-size: 18px;
      font-weight: 500;
    }

    .van-icon {
      font-size: 20px;
      cursor: pointer;
    }
  }

  .filter-form {
    .filter-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #323233;
        margin-bottom: 12px;
      }

      .van-field {
        margin-bottom: 8px;
      }

      .van-radio-group {
        display: flex;
        gap: 16px;
        padding: 8px 0;

        .van-radio {
          flex: none;
        }
      }
    }

    .filter-actions {
      display: flex;
      gap: 12px;
      margin-top: 32px;
      padding-top: 16px;
      border-top: 1px solid #ebedf0;

      .van-button {
        flex: 1;
      }
    }
  }
}

// 详情弹窗样式
.detail-content {
  padding: 16px;
  height: 100%;
  background: #fff;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebedf0;
    margin-bottom: 16px;

    span {
      font-size: 18px;
      font-weight: 500;
    }

    .van-icon {
      font-size: 20px;
      cursor: pointer;
    }
  }

  .detail-body {
    height: calc(100% - 60px);
    overflow-y: auto;

    .detail-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #323233;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f7f8fa;
      }

      .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f7f8fa;

        &:last-child {
          border-bottom: none;
        }

        .label {
          font-size: 14px;
          color: #646566;
          flex: none;
          width: 100px;
        }

        .value {
          font-size: 14px;
          color: #323233;
          text-align: right;
          flex: 1;

          &.amount {
            font-weight: 500;
            font-size: 16px;

            &.positive {
              color: #07c160;
            }

            &.negative {
              color: #ee0a24;
            }
          }

          &.transaction-type {
            font-weight: 500;
            color: #323233;
          }
        }
      }
    }
  }
}

.filter-popup-content {
  padding: 0 16px 16px 16px;
  background: #fff;
  min-height: 60vh;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.filter-popup-header {
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  padding: 18px 0 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.filter-popup-body {
  flex: 1 1 auto;
  overflow-y: auto;
  padding-top: -2px;
  background-color: #F2F3F5;
}

.filter-row {
  display: flex;
  // margin-bottom: 20px;
  min-height: 40px;

  .filter-label-left {
    width: 80px;
    // background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #646566;
    border-radius: 8px 0 0 8px;
    flex-shrink: 0;
  }

  .filter-content-right {
    flex: 1;
    padding-left: 16px;
    background-color: #fff;
    padding: 22px;
    padding-top: -px;

    // 日期快速选项样式
    .date-quick-options {
      display: flex;
      gap: 12px;
      margin-bottom: 12px;

      .quick-btn {
        min-width: 50px;
        font-size: 14px;
        border-radius: 6px;
        padding: 0 12px;
        height: 32px;
      }
    }

    // 日期范围字段样式
    .date-range-fields {
      // display: flex;
      align-items: center;
      gap: 8px;

      .date-field {
        flex: 1;
        background: #f7f8fa;
        border-radius: 6px;
        font-size: 14px;
        padding: 0 12px;
        height: 36px;
        line-height: 36px;
      }

      .date-sep {
        color: #c8c9cc;
        margin: 0 4px;
        font-size: 14px;
      }
    }

    // 金额范围字段样式
    .amount-range-fields {
      display: flex;
      align-items: center;
      gap: 8px;

      .amount-field {
        flex: 1;
        // background: #f7f8fa;

        font-size: 14px;
        padding: 0 12px;
        padding-left: 8px;
        height: 36px;
        line-height: 36px;
      }

      .amount-sep {
        color: #c8c9cc;
        margin: 0 4px;
        font-size: 14px;
      }
    }

    // 收支类型选项样式
    .income-type-options {
      display: flex;
      gap: 12px;
      margin-top: 16px;

      .income-type-btn {
        min-width: 50px;
        font-size: 14px;
        border-radius: 6px;
        padding: 0 12px;
        height: 32px;
      }
    }

    // 变动类型网格样式
    .change-type-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      margin-bottom: 8px;

      .change-type-btn {
        font-size: 13px;
        border-radius: 6px;
        padding: 6px 8px;
        height: 32px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    // 更多选项样式
    .more-options {
      text-align: right;
      margin-top: 8px;

      .more-btn {
        font-size: 13px;
        border-radius: 6px;
        padding: 4px 12px;
        height: 28px;
        color: #646566;
        border: 1px solid #ebedf0;
      }
    }
  }
}

.filter-popup-actions {
  display: flex;
  gap: 12px;
  margin-top: 12px;
  padding-bottom: env(safe-area-inset-bottom);

  .van-button {
    flex: 1;
    font-size: 16px;
    border-radius: 8px;
    height: 44px;
  }
}
</style>