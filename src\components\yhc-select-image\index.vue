<template>
  <div class="wrapper-uploader yhc-uploader">
    <van-field v-model="fieldValue" readonly :border="false" :name="config.key" :label="config.label"
      :placeholder="config.placeholder" :rules="config.rules" :disabled="config.disabled" input-align="right">
      <!-- <template #right-icon>
        <van-icon name="add" size="24" />
      </template> -->
    </van-field>
    <van-uploader v-model="fileList" :name="config.key" :multiple="config.multiple" :max-count="config['max-count']"
      :preview-size="config['preview-size']" :deletable="config.deletable" :reupload="config.reupload"
      :after-read="afterRead" :disabled="config.disabled" :accept="config.accept" @delete="onDelete">
      <template #preview-cover="item" v-if="config['preview-cover']">
        <div class="preview-cover" @click="onPreFile(item)">预览</div>
      </template>
    </van-uploader>
  </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance } from "vue";
import { useLoginStore } from "@/store/dingLogin";
import { deepAssign } from "@/untils";
import { showToast } from "vant";
import { useRouter } from "vue-router";
const router = useRouter();
const { proxy } = getCurrentInstance();
const app = useLoginStore();
let config = {
  // 基础配置
  label: "图片选择",       // 字段标签 (字符串) - 显示在上传区域上方的标签文字
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"images", "avatar"
  placeholder: "请选择图片", // 占位符 (字符串) - 未上传图片时显示的提示文字
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用上传, false: 可正常上传
  rules: [],               // 验证规则 (数组) - 表单验证规则配置

  // 上传配置
  multiple: false,         // 是否支持多选 (布尔值) - true: 可选择多张图片, false: 只能选择一张
  "max-count": 1,          // 最大上传数量 (数字) - 限制最多上传的图片数量
  accept: "image/*",       // 接受的文件类型 (字符串) - "image/*": 所有图片, "image/png,image/jpg": 指定格式

  // 显示配置
  "preview-size": 70,      // 预览图尺寸 (数字) - 上传后预览图的尺寸，单位为px
  deletable: true,         // 是否可删除 (布尔值) - true: 显示删除按钮, false: 不可删除
  reupload: false,         // 是否支持重新上传 (布尔值) - true: 支持重新选择文件, false: 不支持
  "preview-cover": false,  // 是否显示预览遮罩 (布尔值) - true: 显示"预览"按钮, false: 不显示

  // 上传接口配置
  baseURL: import.meta.env.VITE_APP_FILE_UPLOAD, // 上传服务器地址 (字符串) - 文件上传的服务器基础URL
  url: "file/post_upload", // 上传接口路径 (字符串) - 文件上传的API接口路径
};
let fileList = ref([]);
let fieldValue = ref("");

const props = defineProps({
  config: Object,
  form: Object,
});

props.config && deepAssign(config, props.config);
var initData = () => {
  if (Array.isArray(props.form[config.key])) {
    fileList.value = props.form[config.key].map((el) => ({ url: el })) || [];
  } else {
    console.log("select-image组件传入值格式异常", props.form[config.key]);
  }
  fieldValue.value = props.form[config.key]
    ? JSON.stringify(props.form[config.key])
    : "";
};
initData();

const onPreFile = (item) => {
  // router.push({
  //   name:"pdfView",
  //   params:item
  // });
  window.location.href = item.url || item;
};
const afterRead = (file) => {
  file.status = "uploading";
  file.message = "上传中...";
  proxy
    .$upload(
      config.url,
      {
        file: file.file,
      },
      { baseURL: config.baseURL }
    )
    .then((res) => {
      console.log(res);
      if (!res.errcode) {
        file.status = "done";
        file.message = "";
        file.url = res.result;
        props.form[config.key] = fileList.value.map((el) => el.url);
        fieldValue.value = props.form[config.key].length
          ? JSON.stringify(props.form[config.key])
          : "";
      } else {
        throw res.errmsg;
      }
    })
    .catch((err) => {
      console.log(err);
      file.status = "failed";
      file.message = "上传失败";
    });
};
const onDelete = (e) => {
  props.form[config.key].splice(
    props.form[config.key].findIndex((el) => el == e.url),
    1
  );
};
</script>
<style lang="scss">
.wrapper-uploader {
  background-color: #fff;

  .van-uploader {
    margin-left: 16px;
  }
}

.yhc-uploader {
  .van-field__control--right {
    visibility: hidden;
  }
}

.preview-cover {
  position: absolute;
  bottom: 0;
  box-sizing: border-box;
  width: 100%;
  padding: 4px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
}
</style>
